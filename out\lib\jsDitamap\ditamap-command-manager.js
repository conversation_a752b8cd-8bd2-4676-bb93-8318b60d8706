import { notifyUser } from "lib/util";
export class DitamapCommandManager {
    constructor(tree, setRoot) {
        this.tree = tree;
        this.setRoot = setRoot;
        this.history = [];
    }
    async execute(command) {
        try {
            command.execute();
            this.history.push(command);
            this.unsetCut(command);
            await this.tree.rebuildTree();
            this.setRoot(this.tree.getRoot());
            return true;
        }
        catch (err) {
            notifyUser("warn", err);
            return false;
        }
    }
    /**
     * Cancel the cut operation if the command is not a cut
     */
    unsetCut(command) {
        if (!(command instanceof CutCommand)) {
            this.tree.getRoot().context.cutId = null;
        }
        this.tree.getRoot().context.cutId = null;
    }
    undo() {
        this.history.pop()?.undo();
    }
}
export class CutCommand {
    constructor(node) {
        this.node = node;
    }
    execute() {
        this.node.cut();
    }
    undo() {
        console.log("Undo");
    }
}
export class InsertCommand {
    constructor(node, file) {
        this.node = node;
        this.file = file;
    }
    execute() {
        if (this.file.rootElementName === "bookmap") {
            throw Error("Cannot insert bookmap into bookmap");
        }
        this.node.insertNodeFromFile(this.file);
    }
    undo() {
        console.log("Undo");
    }
}
export class RemoveCommand {
    constructor(node) {
        this.node = node;
    }
    execute() {
        this.node.remove();
    }
    undo() {
        console.log("Delete");
    }
}
//# sourceMappingURL=ditamap-command-manager.js.map