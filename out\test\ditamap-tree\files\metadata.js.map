{"version": 3, "file": "metadata.js", "sourceRoot": "", "sources": ["../../../../src/test/ditamap-tree/files/metadata.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,UAAU,GAAa;IAClC,SAAS,EAAE,gCAAgC;IAC3C,WAAW,EAAE,CAAC,CAAC;IACf,UAAU,EAAE,CAAC,CAAC;IACd,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,WAAW;IACpB,QAAQ,EAAE,IAAI;IACd,UAAU,EAAE,QAAQ;IACpB,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,UAAU;IACpB,IAAI,EAAE,WAAW;IACjB,QAAQ,EAAE,+BAA+B;IACzC,SAAS,EAAE,8CAA8C;IACzD,eAAe,EAAE,SAAS;IAC1B,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,CAAC;CACV,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAAa;IACtC,SAAS,EAAE,sDAAsD;IACjE,WAAW,EAAE,CAAC,CAAC;IACf,UAAU,EAAE,CAAC,CAAC;IACd,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,WAAW;IACpB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;IACf,UAAU,EAAE,QAAQ;IACpB,QAAQ,EAAE,UAAU;IACpB,IAAI,EAAE,eAAe;IACrB,QAAQ,EAAE,mCAAmC;IAC7C,SAAS,EAAE,oDAAoD;IAC/D,eAAe,EAAE,YAAY;IAC7B,KAAK,EAAE,eAAe;IACtB,MAAM,EAAE,CAAC;CACV,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAa;IACnC,SAAS,EAAE,YAAY;IACvB,WAAW,EAAE,CAAC,CAAC;IACf,UAAU,EAAE,CAAC,CAAC;IACd,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,WAAW;IACpB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;IACf,UAAU,EAAE,QAAQ;IACpB,QAAQ,EAAE,UAAU;IACpB,IAAI,EAAE,iCAAiC;IACvC,QAAQ,EAAE,qDAAqD;IAC/D,SAAS,EAAE,6DAA6D;IACxE,eAAe,EAAE,KAAK;IACtB,KAAK,EAAE,iCAAiC;IACxC,MAAM,EAAE,CAAC;CACV,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAAa;IAC7C,SAAS,EAAE,4BAA4B;IACvC,WAAW,EAAE,CAAC,UAAU;IACxB,UAAU,EAAE,CAAC,UAAU;IACvB,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,WAAW;IACpB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,sBAAsB;IAChC,YAAY,EAAE,kCAAkC;IAChD,SAAS,EAAE,YAAY;IACvB,UAAU,EAAE,gBAAgB;IAC5B,QAAQ,EAAE,UAAU;IACpB,IAAI,EAAE,wBAAwB;IAC9B,WAAW,EAAE,MAAM;IACnB,QAAQ,EAAE,4CAA4C;IACtD,SAAS,EAAE,oDAAoD;IAC/D,eAAe,EAAE,SAAS;IAC1B,KAAK,EAAE,oBAAoB;IAC3B,aAAa,EAAE,sBAAsB;IACrC,UAAU,EAAE,eAAe;IAC3B,MAAM,EAAE,CAAC;CACV,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAa;IAC1C,UAAU,EAAE;QACV,UAAU,EAAE,QAAQ;QACpB,aAAa,EAAE,KAAK;KACrB;IACD,SAAS,EAAE,YAAY;IACvB,WAAW,EAAE,CAAC,UAAU;IACxB,UAAU,EAAE,CAAC,UAAU;IACvB,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,WAAW;IACpB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,sBAAsB;IAChC,YAAY,EAAE,kCAAkC;IAChD,SAAS,EAAE,YAAY;IACvB,UAAU,EAAE,gBAAgB;IAC5B,QAAQ,EAAE,UAAU;IACpB,IAAI,EAAE,uBAAuB;IAC7B,WAAW,EAAE,MAAM;IACnB,QAAQ,EAAE,2CAA2C;IACrD,SAAS,EAAE,mDAAmD;IAC9D,eAAe,EAAE,KAAK;IACtB,KAAK,EAAE,eAAe;IACtB,aAAa,EAAE,sBAAsB;IACrC,UAAU,EAAE,eAAe;IAC3B,MAAM,EAAE,CAAC;CACV,CAAC", "sourcesContent": ["export const BEGIN_FILE: FileMeta = {\r\n  ditaClass: \"- topic/topic concept/concept \",\r\n  imageHeight: -1,\r\n  imageWidth: -1,\r\n  isXml: true,\r\n  kbResId: \"Xdfv_1612\",\r\n  lblSeqId: 1612,\r\n  lockorType: \"NoLock\",\r\n  lineageId: 1612,\r\n  mimeType: \"text/xml\",\r\n  name: \"begin.xml\",\r\n  resLblId: \"/Content/begin_xi1612_1_1.xml\",\r\n  resPathId: \"/Content/sample/dita/phones/topics/begin.xml\",\r\n  rootElementName: \"concept\",\r\n  title: \"Begin\",\r\n  verNum: 0,\r\n};\r\n\r\nexport const DUAL_BAND_FILE: FileMeta = {\r\n  ditaClass: \"- topic/topic concept/concept glossentry/glossentry \",\r\n  imageHeight: -1,\r\n  imageWidth: -1,\r\n  isXml: true,\r\n  kbResId: \"Xdfv_1582\",\r\n  lblSeqId: 1582,\r\n  lineageId: 1582,\r\n  lockorType: \"NoLock\",\r\n  mimeType: \"text/xml\",\r\n  name: \"dual_band.xml\",\r\n  resLblId: \"/Content/dual_band_xi1582_1_1.xml\",\r\n  resPathId: \"/Content/sample/dita/phones/glossary/dual_band.xml\",\r\n  rootElementName: \"glossentry\",\r\n  title: \"dual_band.xml\",\r\n  verNum: 0,\r\n};\r\n\r\nexport const DITA20_FILE: FileMeta = {\r\n  ditaClass: \"- map/map \",\r\n  imageHeight: -1,\r\n  imageWidth: -1,\r\n  isXml: true,\r\n  kbResId: \"Xdfv_1683\",\r\n  lblSeqId: 1683,\r\n  lineageId: 1683,\r\n  lockorType: \"NoLock\",\r\n  mimeType: \"text/xml\",\r\n  name: \"dita20_quickstart_guide.ditamap\",\r\n  resLblId: \"/Content/dita20_quickstart_guide_xi1683_1_1.ditamap\",\r\n  resPathId: \"/Content/sample/dita/dita20/dita20_quickstart_guide.ditamap\",\r\n  rootElementName: \"map\",\r\n  title: \"DITA 2.0 Phone Quickstart Guide\",\r\n  verNum: 0,\r\n};\r\n\r\nexport const PHONES_1_BOOKMAP_FILE: FileMeta = {\r\n  ditaClass: \"- map/map bookmap/bookmap \",\r\n  imageHeight: -2147483648,\r\n  imageWidth: -2147483648,\r\n  isXml: true,\r\n  kbResId: \"Xdfv_1574\",\r\n  lblSeqId: 1574,\r\n  lineageId: 1574,\r\n  lockDate: \"2025-09-02T13:25:51Z\",\r\n  lockLocation: \"wex:cc:room:-8630300732938674629\",\r\n  lockOwner: \"concurrent\",\r\n  lockorType: \"RoomConcurrent\",\r\n  mimeType: \"text/xml\",\r\n  name: \"Phone1_Bookmap.ditamap\",\r\n  permissions: \"RUDP\",\r\n  resLblId: \"/Content/Phone1_Bookmap_xi1574_1_1.ditamap\",\r\n  resPathId: \"/Content/sample/dita/phones/Phone1_Bookmap.ditamap\",\r\n  rootElementName: \"bookmap\",\r\n  title: \"Phone 1 User Guide\",\r\n  verCreateDate: \"2025-08-22T18:49:40Z\",\r\n  verCreator: \"administrator\",\r\n  verNum: 1,\r\n};\r\n\r\nexport const SUBMAP_PHONES_FILE: FileMeta = {\r\n  capability: {\r\n    capability: \"Author\",\r\n    workflowBased: false,\r\n  },\r\n  ditaClass: \"- map/map \",\r\n  imageHeight: -2147483648,\r\n  imageWidth: -2147483648,\r\n  isXml: true,\r\n  kbResId: \"Xdfv_1606\",\r\n  lblSeqId: 1606,\r\n  lineageId: 1606,\r\n  lockDate: \"2025-09-02T13:25:50Z\",\r\n  lockLocation: \"wex:cc:room:-8630300732938674629\",\r\n  lockOwner: \"concurrent\",\r\n  lockorType: \"RoomConcurrent\",\r\n  mimeType: \"text/xml\",\r\n  name: \"submap_phones.ditamap\",\r\n  permissions: \"RUDP\",\r\n  resLblId: \"/Content/submap_phones_xi1609_1_1.ditamap\",\r\n  resPathId: \"/Content/sample/dita/phones/submap_phones.ditamap\",\r\n  rootElementName: \"map\",\r\n  title: \"Submap Phones\",\r\n  verCreateDate: \"2025-08-22T18:49:40Z\",\r\n  verCreator: \"administrator\",\r\n  verNum: 1,\r\n};\r\n"]}