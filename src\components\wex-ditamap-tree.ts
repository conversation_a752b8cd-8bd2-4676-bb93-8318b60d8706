import "bds-tree";
import { LitElement, TemplateResult, html } from "lit";
import { DitamapTree } from "../lib/jsDitamap/ditamap-tree";
import { customElement, property, query } from "lit/decorators.js";
import { DitamapNode } from "lib/jsDitamap/ditamap-node";
import "./wex-ditamap-tree-toolbar.ts";
import "./wex-ditamap-icon.ts";
import { BDSTree } from "bds-tree";
import { classMap } from "lit/directives/class-map.js";
import {
  CopyCommand,
  CutCommand,
  DitamapCommandManager,
  InsertCommand,
  PasteCommand,
  RemoveCommand,
} from "lib/jsDitamap/ditamap-command-manager";

@customElement("wex-ditamap-tree")
export class WexDitamapTree extends LitElement {
  treeConfig: any;
  commandManager?: DitamapCommandManager;
  tree: DitamapTree | null = null;
  @property({ type: Object })
  root: DitamapNode | null = null;
  @property({ type: String })
  rootMap: string;
  @property({ type: Object })
  selectedNode: DitamapNode | null = null;
  @property({ type: String })
  status: "success" | "error" | "loading" = "loading";
  @query("bds-tree")
  treeEl?: BDSTree<DitamapNode>;

  constructor() {
    super();
    this.treeConfig = {
      labelPath: "title",
      idPath: "id",
      childPath: "children",
      showRoot: true,
      iconSet: "ditamap",
      hasUnselect: true,
      nodeRenderer: this.nodeRenderer.bind(this),
    };
    this.rootMap = "";
  }

  async connectedCallback() {
    super.connectedCallback();
    console.log("MAP: ", this.rootMap);
    this.tree = new DitamapTree(this.rootMap);
    this.status = "loading";
    await this.tree.load();
    this.root = this.tree.getRoot()!;
    this.status = "success";
  }

  updated() {
    if (!this.commandManager && this.tree && this.root) {
      this.commandManager = new DitamapCommandManager(this.tree, (root) => {
        this.root = root;
      });
    }
  }

  async handleCut() {
    if (this.selectedNode) {
      await this.commandManager?.execute(new CutCommand(this.selectedNode));
    }
  }

  async handleCopy() {
    if (this.selectedNode) {
      await this.commandManager?.execute(new CopyCommand(this.selectedNode));
    }
  }

  async handlePaste() {
    if (this.selectedNode) {
      await this.commandManager?.execute(new PasteCommand(this.selectedNode));
    }
  }

  async handleInsert() {
    if (this.selectedNode) {
      await this.commandManager?.execute(
        new InsertCommand(this.selectedNode, TEMP_GLOSSENTRY)
      );
    }
  }

  async handleRemove() {
    if (this.selectedNode) {
      let result = await this.commandManager?.execute(
        new RemoveCommand(this.selectedNode)
      );
      if (result) {
        this.selectedNode = null;
      }
    }
  }

  handleTreeClick(e) {
    if (e.detail.isSelected) {
      this.selectedNode = e.detail.node;
    } else {
      this.selectedNode = null;
    }
  }

  /**
   * Controls the display of nodes in the tree
   */
  nodeRenderer(node: WexDitamapNode): TemplateResult {
    if (node.context.cutId === node.key) {
      console.log("CUT: ", node);
    }

    return html` <style>
        .is-cut {
          opacity: 0.25;
        }
      </style>
      <span
        class=${classMap({
          "is-cut": node.context?.cutId === node.key,
        })}
      >
        <wex-ditamap-icon icon=${node?.type}></wex-ditamap-icon>
        ${node.label}</span
      >`;
  }

  render() {
    return html` ${this.status == "loading"
      ? html`<div>Loading...</div>`
      : this.status == "error"
        ? html`<div>Error</div>`
        : html` <wex-ditamap-tree-toolbar
              .hasSelection="${!!this.selectedNode}"
              .hasBuffer="${!!this.tree?.context.buffer}"
              @insert="${this.handleInsert}"
              @remove="${this.handleRemove}"
              @cut="${this.handleCut}"
              @copy="${this.handleCopy}"
              @paste="${this.handlePaste}"
            ></wex-ditamap-tree-toolbar>
            <span
              >Selected: ${this.selectedNode?.title || "None"}
              ${this.selectedNode?.id}</span
            >
            <bds-tree
              @treeClick=${this.handleTreeClick}
              .config=${this.treeConfig}
              .root=${this.root}
            ></bds-tree>`}`;
  }
}

const TEMP_FILE = {
  ditaClass: "- topic/topic concept/concept ",
  imageHeight: -1,
  imageWidth: -1,
  isXml: true,
  kbResId: "Xdfv_1612",
  lblSeqId: 1612,
  lockorType: "NoLock",
  lineageId: 1612,
  mimeType: "text/xml",
  name: "begin.xml",
  resLblId: "/Content/begin_xi1609_1_1.xml",
};
// Content/submap_phones_xi1606_1_1.ditamap
const TEMP_MAP = {
  ditaClass: "- map/map ",
  imageHeight: -2147483648,
  imageWidth: -2147483648,
  isXml: true,
  kbResId: "Xdfv_1680",
  lblSeqId: 1680,
  lineageId: 1680,
  lockorType: "NoLock",
  mimeType: "text/xml",
  name: "dita20_quickstart_guide.ditamap",
  permissions: "RUDP",
  resLblId: "/Content/dita20_quickstart_guide_xi1680_1_1.ditamap",
  resPathId: "/Content/sample/dita/dita20/dita20_quickstart_guide.ditamap",
  rootElementName: "map",
  title: "DITA 2.0 Phone Quickstart Guide",
  verCreateDate: "2025-08-22T18:50:12Z",
  verCreator: "administrator",
  verNum: 1,
};

const TEMP_BOOKMAP = {
  ditaClass: "- map/map bookmap/bookmap ",
  imageHeight: -2147483648,
  imageWidth: -2147483648,
  isXml: true,
  kbResId: "Xdfv_1574",
  lblSeqId: 1574,
  lineageId: 1574,
  lockDate: "2025-09-02T13:25:51Z",
  lockLocation: "wex:cc:room:-8630300732938674629",
  lockOwner: "concurrent",
  lockorType: "RoomConcurrent",
  mimeType: "text/xml",
  name: "Phone1_Bookmap.ditamap",
  permissions: "RUDP",
  resLblId: "/Content/Phone1_Bookmap_xi1574_1_1.ditamap",
  resPathId: "/Content/sample/dita/phones/Phone1_Bookmap.ditamap",
  rootElementName: "bookmap",
  title: "Phone 1 User Guide",
  verCreateDate: "2025-08-22T18:49:40Z",
  verCreator: "administrator",
  verNum: 1,
};

const TEMP_GLOSSENTRY = {
  ditaClass: "- topic/topic concept/concept glossentry/glossentry ",
  imageHeight: -2147483648,
  imageWidth: -2147483648,
  isXml: true,
  kbResId: "Xdfv_1578",
  lblSeqId: 1578,
  lineageId: 1578,
  lockorType: "NoLock",
  mimeType: "text/xml",
  name: "charger.xml",
  permissions: "RUDP",
  resLblId: "/Content/charger_xi1578_1_1.xml",
  resPathId: "/Content/sample/dita/phones/glossary/charger.xml",
  rootElementName: "glossentry",
  title: "charger.xml",
  verCreateDate: "2025-08-22T18:49:40Z",
  verCreator: "administrator",
  verNum: 1,
};

interface WexDitamapNode extends DitamapNode {
  key: string;
  label: string;
}
