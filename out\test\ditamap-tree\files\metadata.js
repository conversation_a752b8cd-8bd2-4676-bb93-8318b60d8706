export const BEGIN_FILE = {
    ditaClass: "- topic/topic concept/concept ",
    imageHeight: -1,
    imageWidth: -1,
    isXml: true,
    kbResId: "Xdfv_1612",
    lblSeqId: 1612,
    lockorType: "NoLock",
    lineageId: 1612,
    mimeType: "text/xml",
    name: "begin.xml",
    resLblId: "/Content/begin_xi1612_1_1.xml",
    resPathId: "/Content/sample/dita/phones/topics/begin.xml",
    rootElementName: "concept",
    title: "Begin",
    verNum: 0,
};
export const DUAL_BAND_FILE = {
    ditaClass: "- topic/topic concept/concept glossentry/glossentry ",
    imageHeight: -1,
    imageWidth: -1,
    isXml: true,
    kbResId: "Xdfv_1582",
    lblSeqId: 1582,
    lineageId: 1582,
    lockorType: "NoLock",
    mimeType: "text/xml",
    name: "dual_band.xml",
    resLblId: "/Content/dual_band_xi1582_1_1.xml",
    resPathId: "/Content/sample/dita/phones/glossary/dual_band.xml",
    rootElementName: "glossentry",
    title: "dual_band.xml",
    verNum: 0,
};
export const DITA20_FILE = {
    ditaClass: "- map/map ",
    imageHeight: -1,
    imageWidth: -1,
    isXml: true,
    kbResId: "Xdfv_1683",
    lblSeqId: 1683,
    lineageId: 1683,
    lockorType: "NoLock",
    mimeType: "text/xml",
    name: "dita20_quickstart_guide.ditamap",
    resLblId: "/Content/dita20_quickstart_guide_xi1683_1_1.ditamap",
    resPathId: "/Content/sample/dita/dita20/dita20_quickstart_guide.ditamap",
    rootElementName: "map",
    title: "DITA 2.0 Phone Quickstart Guide",
    verNum: 0,
};
export const PHONES_1_BOOKMAP_FILE = {
    ditaClass: "- map/map bookmap/bookmap ",
    imageHeight: -2147483648,
    imageWidth: -2147483648,
    isXml: true,
    kbResId: "Xdfv_1574",
    lblSeqId: 1574,
    lineageId: 1574,
    lockDate: "2025-09-02T13:25:51Z",
    lockLocation: "wex:cc:room:-8630300732938674629",
    lockOwner: "concurrent",
    lockorType: "RoomConcurrent",
    mimeType: "text/xml",
    name: "Phone1_Bookmap.ditamap",
    permissions: "RUDP",
    resLblId: "/Content/Phone1_Bookmap_xi1574_1_1.ditamap",
    resPathId: "/Content/sample/dita/phones/Phone1_Bookmap.ditamap",
    rootElementName: "bookmap",
    title: "Phone 1 User Guide",
    verCreateDate: "2025-08-22T18:49:40Z",
    verCreator: "administrator",
    verNum: 1,
};
export const SUBMAP_PHONES_FILE = {
    capability: {
        capability: "Author",
        workflowBased: false,
    },
    ditaClass: "- map/map ",
    imageHeight: -2147483648,
    imageWidth: -2147483648,
    isXml: true,
    kbResId: "Xdfv_1606",
    lblSeqId: 1606,
    lineageId: 1606,
    lockDate: "2025-09-02T13:25:50Z",
    lockLocation: "wex:cc:room:-8630300732938674629",
    lockOwner: "concurrent",
    lockorType: "RoomConcurrent",
    mimeType: "text/xml",
    name: "submap_phones.ditamap",
    permissions: "RUDP",
    resLblId: "/Content/submap_phones_xi1609_1_1.ditamap",
    resPathId: "/Content/sample/dita/phones/submap_phones.ditamap",
    rootElementName: "map",
    title: "Submap Phones",
    verCreateDate: "2025-08-22T18:49:40Z",
    verCreator: "administrator",
    verNum: 1,
};
//# sourceMappingURL=metadata.js.map