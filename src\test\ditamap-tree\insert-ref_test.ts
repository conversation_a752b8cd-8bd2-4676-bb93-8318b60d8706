import {
  MapReferenceNode,
  RootMapNode,
  TopicReferenceNode,
} from "../../lib/jsDitamap/ditamap-node";

import { expect } from "@open-wc/testing";
import {
  createXmlElement,
  MAP_DITA20,
  MAP_SUBMAP_PHONES,
  PART_SUBMAP_PHONES,
} from "./data/elements";
import * as metadata from "./files/metadata";
import { BEGIN_TOPIC_REF, GLOSSARY_TOPICHEAD, PHONES_1_BOOKMAP } from "./files/phones_1_bookmap";

describe("Map Reference Parent", () => {
  it("Inserts a concept as a topicref", () => {
    let refElement = createXmlElement(PART_SUBMAP_PHONES);
    let structuralElement = createXmlElement(MAP_SUBMAP_PHONES);
    let parent = new MapReferenceNode({
      refElement: refElement,
      structuralElement: structuralElement,
      containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
    });

    parent.insertNodeFromFile(metadata.BEGIN_FILE);
    let target = parent.structuralElement.querySelector(
      `[href='${metadata.BEGIN_FILE.resLblId}']`
    )!;

    expect(target.tagName).to.equal("topicref");
    expect(target.getAttribute("href")).to.equal(metadata.BEGIN_FILE.resLblId);
  });

  it("Inserts a glossentry as a topicref", () => {
    let refElement = createXmlElement(PART_SUBMAP_PHONES);
    let structuralElement = createXmlElement(MAP_SUBMAP_PHONES);
    let parent = new MapReferenceNode({
      refElement: refElement,
      structuralElement: structuralElement,
      containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
    });

    parent.insertNodeFromFile(metadata.DUAL_BAND_FILE);

    let target = parent.structuralElement.querySelector(
      `[href='${metadata.DUAL_BAND_FILE.resLblId}']`
    )!;
    expect(target.tagName).to.equal("glossref");
    expect(target.getAttribute("href")).to.equal(
      metadata.DUAL_BAND_FILE.resLblId
    );
  });

  it("Inserts a map as a mapref", () => {
    let refElement = createXmlElement(PART_SUBMAP_PHONES);
    let structuralElement = createXmlElement(MAP_SUBMAP_PHONES);
    let parent = new MapReferenceNode({
      refElement: refElement,
      structuralElement: structuralElement,
      containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
    });

    parent.insertNodeFromFile(metadata.DITA20_FILE);
    let target = parent.structuralElement.querySelector(
      `[href='${metadata.DITA20_FILE.resLblId}']`
    )!;
    expect(target.tagName).to.equal("mapref");
    expect(target.getAttribute("href")).to.equal(metadata.DITA20_FILE.resLblId);
  });
});

/** Needs further tests as to where it can be inserted */
describe("Bookmap Parent", () => {
  it("Inserts a ditamap as a part", () => {
    let structuralElement = createXmlElement(PHONES_1_BOOKMAP);
    let parent = new RootMapNode({
      rootElement: structuralElement,
      rootMapName: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
    });
    parent.insertNodeFromFile(metadata.DITA20_FILE);
    let target = parent.structuralElement.querySelector(
      "[href='/Content/dita20_quickstart_guide_xi1683_1_1.ditamap']"
    )!;
    expect(target.tagName).to.equal("part");
    expect(target.getAttribute("href")).to.equal(
      "/Content/dita20_quickstart_guide_xi1683_1_1.ditamap"
    );
  });

  it("Inserts a topicref as a chapter", () => {
    let structuralElement = createXmlElement(PHONES_1_BOOKMAP);
    let parent = new RootMapNode({
      rootElement: structuralElement,
      rootMapName: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
    });
    parent.insertNodeFromFile(metadata.BEGIN_FILE);
    let target = parent.structuralElement.querySelector(
      "[href='/Content/begin_xi1612_1_1.xml']"
    )!;
    expect(target.tagName).to.equal("chapter");
    expect(target.getAttribute("href")).to.equal(
      "/Content/begin_xi1612_1_1.xml"
    );
  });

  it("Blocks a glossentry", () => {
    let structuralElement = createXmlElement(PHONES_1_BOOKMAP);
    let parent = new RootMapNode({
      rootElement: structuralElement,
      rootMapName: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
    });
    expect(() => parent.insertNodeFromFile(metadata.DUAL_BAND_FILE)).to.throw();
  });
});

describe("Topic Reference Parent", () => {
  it("Inserts a ditamap as a mapref", () => {
    let refElement = createXmlElement(BEGIN_TOPIC_REF);
    let parent = new TopicReferenceNode({
      refElement: refElement,
      containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      context: {
        workspace: new Map(),
        metadata: new Map(),
        dirty: new Set(),
        cutId: null,
        buffer: null,
      },
    });
    parent.insertNodeFromFile(metadata.DITA20_FILE);
    let target = parent.refElement.querySelector(
      `[href='${metadata.DITA20_FILE.resLblId}']`
    )!;
    expect(target.tagName).to.equal("mapref");
    expect(target.getAttribute("href")).to.equal(metadata.DITA20_FILE.resLblId);
  });

  it("Inserts a topicref as a topicref", () => {
    let refElement = createXmlElement(BEGIN_TOPIC_REF);
    let parent = new TopicReferenceNode({
      refElement: refElement,
      containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      context: {
        workspace: new Map(),
        metadata: new Map(),
        dirty: new Set(),
        cutId: null,
        buffer: null,
      },
    });
    parent.insertNodeFromFile(metadata.BEGIN_FILE);
    let target = parent.refElement.querySelector(
      `[href='${metadata.BEGIN_FILE.resLblId}']`
    )!;
    expect(target.tagName).to.equal("topicref");
    expect(target.getAttribute("href")).to.equal(metadata.BEGIN_FILE.resLblId);
  });
});
