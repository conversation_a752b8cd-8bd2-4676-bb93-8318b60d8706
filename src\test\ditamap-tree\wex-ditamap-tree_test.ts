import { setupWorker } from "msw/browser";
import { handlers } from "./msw/handlers";
import { expect, fixture, html } from "@open-wc/testing";
import { waitUntil } from "@open-wc/testing-helpers";
import { TreeTestDriver } from "./drivers/bds-tree-driver";
import "../../components/wex-ditamap-tree";
import { WexDitamapTree } from "../../components/wex-ditamap-tree";

describe("Tree rendering", () => {
  const worker = setupWorker(...handlers);

  before(async () => {
    await worker.start();
  });

  after(() => {
    worker.stop();
  });

  it("shows the tree when loading succeeds", async () => {
    const el: WexDitamapTree = await fixture(html`<wex-ditamap-tree rootMap="/Content/Phone1_Bookmap_xi1577_1_1.ditamap"></wex-ditamap-tree>`);

    // Wait for the tree to load
    await waitUntil(() => el.status === "success", "Did not load tree");
    let treeEl = el.shadowRoot?.querySelector("bds-tree");
    const tree = new TreeTestDriver(treeEl as HTMLElement);
    const node = await tree.getNodeByKey(
      "/Content/Phone1_Bookmap_xi1577_1_1.ditamap"
    );
    expect(node.label).to.equal("Phone 1 User Guide");
  });

  it.skip("shows a loading state", () => {});
  it.skip("shows an error when loading fails");
});

describe("Delete", () => {
  it.skip("deletes a node", () => {});
});

describe("Insert", () => {
  it.skip("inserts a topic", () => {});
  it.skip("inserts a map", () => {});
  it.skip("blocks inserting a bookmap", () => {});
  it.skip("blocks a self reference", () => {});
});

export const sleep = async (time: number) => {
  await new Promise((resolve) => setTimeout(resolve, time));
};
