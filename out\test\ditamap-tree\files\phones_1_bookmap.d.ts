export declare const PHONES_1_BOOKMAP = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE bookmap PUBLIC \"-//OASIS//DTD DITA BookMap//EN\" \"/SysSchema/dita/dtd/bookmap/dtd/bookmap.dtd\">\n<bookmap xmlns:ditaarch=\"http://dita.oasis-open.org/architecture/2005/\"\n\tid=\"xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0\" xml:lang=\"en-US\"\n\tclass=\"- map/map bookmap/bookmap \">\n\t<booktitle class=\"- topic/title bookmap/booktitle \">\n\t\t<mainbooktitle class=\"- topic/ph bookmap/mainbooktitle \">\n\t\t\t<?xm-replace_text Main Book Title?>Phone 1 User Guide</mainbooktitle>\n\t</booktitle>\n\n\t<bookmeta class=\"- map/topicmeta bookmap/bookmeta \">\n\t\t<bookid class=\"- topic/data bookmap/bookid \">\n\t\t\t<bookpartno class=\"- topic/data bookmap/bookpartno \">\n\t\t\t\t<?xm-replace_text Book Part Number?>\n\t\t\t</bookpartno>\n\t\t\t<isbn class=\"- topic/data bookmap/isbn \">\n\t\t\t\t<?xm-replace_text ISBN Number?>\n\t\t\t</isbn>\n\t\t</bookid>\n\t\t<bookrights class=\"- topic/data bookmap/bookrights \">\n\t\t\t<copyrfirst class=\"- topic/data bookmap/copyrfirst \">\n\t\t\t\t<year class=\"- topic/ph bookmap/year \">\n\t\t\t\t\t<?xm-replace_text First Year?>\n\t\t\t\t</year>\n\t\t\t</copyrfirst>\n\t\t\t<copyrlast class=\"- topic/data bookmap/copyrlast \">\n\t\t\t\t<year class=\"- topic/ph bookmap/year \">\n\t\t\t\t\t<?xm-replace_text Last Year?>\n\t\t\t\t</year>\n\t\t\t</copyrlast>\n\t\t\t<bookowner class=\"- topic/data bookmap/bookowner \">\n\t\t\t\t<person class=\"- topic/data bookmap/person \">\n\t\t\t\t\t<?xm-replace_text Copyright owner?>\n\t\t\t\t</person>\n\t\t\t</bookowner>\n\t\t</bookrights>\n\t</bookmeta>\n\t<frontmatter class=\"- map/topicref bookmap/frontmatter \">\n\t\t<booklists class=\"- map/topicref bookmap/booklists \">\n\t\t\t<toc class=\"- map/topicref bookmap/toc \" />\n\t\t</booklists>\n\t\t<keydef keys=\"productSpec\" href=\"/Content/phone_1_spec_xi1622_1_1.xml\"\n\t\t\tclass=\"+ map/topicref mapgroup-d/keydef \" />\n\t\t<keydef href=\"/Content/phone1_small_xi1596_1_1.jpg\" format=\"png\" keys=\"phoneImage\"\n\t\t\tclass=\"+ map/topicref mapgroup-d/keydef \" />\n\t</frontmatter>\n\t<part href=\"/Content/submap_phones_xi1609_1_1.ditamap\" format=\"ditamap\"\n\t\tclass=\"- map/topicref bookmap/part \" />\n\t<backmatter class=\"- map/topicref bookmap/backmatter \">\n\t\t<booklists class=\"- map/topicref bookmap/booklists \">\n\t\t\t<indexlist class=\"- map/topicref bookmap/indexlist \" />\n\t\t</booklists>\n\t</backmatter>\n</bookmap>";
export declare const SUBMAP_PHONES = "<map xmlns:ditaarch=\"http://dita.oasis-open.org/architecture/2005/\"\n    id=\"map_1C03AB61C63B4FA6A95F36368EFD4772\" title=\"Submap Phones\" xml:lang=\"en-US\"\n    class=\"- map/map \">\n    <topicref format=\"dita\" href=\"/Content/begin_xi1612_1_1.xml\" class=\"- map/topicref \">\n        <topicref format=\"dita\" href=\"/Content/getting_started_xi1615_1_1.xml\"\n            class=\"- map/topicref \" />\n        <topicref format=\"dita\" href=\"/Content/insert_SIMcard_xi1616_1_1.xml\"\n            class=\"- map/topicref \" />\n        <topicref format=\"dita\" href=\"/Content/charge_battery_xi1613_1_1.xml\"\n            class=\"- map/topicref \" />\n    </topicref>\n    <topicref format=\"dita\" href=\"/Content/basics_xi1610_1_1.xml\" class=\"- map/topicref \">\n        <topicref format=\"dita\" href=\"/Content/overview_handset_xi1621_1_1.xml\"\n            class=\"- map/topicref \" />\n        <topicref format=\"dita\" href=\"/Content/make_and_receive_calls_xi1619_1_1.xml\"\n            class=\"- map/topicref \">\n            <topicref format=\"dita\" href=\"/Content/make_calls_xi1620_1_1.xml\"\n                class=\"- map/topicref \" />\n            <topicref format=\"dita\" href=\"/Content/receive_call_xi1625_1_1.xml\"\n                class=\"- map/topicref \" />\n            <topicref format=\"dita\" href=\"/Content/reject_call_xi1626_1_1.xml\"\n                class=\"- map/topicref \" />\n        </topicref>\n        <topicref format=\"dita\" href=\"/Content/retrieve_voicemail_xi1631_1_1.xml\"\n            class=\"- map/topicref \" />\n        <topicref href=\"/Content/taking_photos_xi1637_1_1.xml\" class=\"- map/topicref \">\n            <topicref format=\"dita\" href=\"/Content/take_photos_xi1636_1_1.xml\"\n                class=\"- map/topicref \" />\n            <topicref href=\"/Content/take_front-facing_photo_xi1635_1_1.xml\" class=\"- map/topicref \" />\n        </topicref>\n        <topicref format=\"dita\" href=\"/Content/listen%20_music_xi1618_1_1.xml\"\n            class=\"- map/topicref \" />\n        <topicref href=\"/Content/exchange_data_xi1614_1_1.xml\" class=\"- map/topicref \" />\n        <topicref format=\"dita\" href=\"/Content/reset_phone_xi1630_1_1.xml\" class=\"- map/topicref \" />\n    </topicref>\n    <topicref format=\"dita\" href=\"/Content/troubleshooting_xi1638_1_1.xml\" class=\"- map/topicref \">\n        <topicref href=\"/Content/screen_frozen_xi1632_1_1.xml\" class=\"- map/topicref \" />\n        <topicref format=\"dita\" href=\"/Content/battery_drain_xi1611_1_1.xml\" class=\"- map/topicref \" />\n    </topicref>\n    <topicref audience=\"technician\" format=\"dita\" href=\"/Content/service_section_xi1633_1_1.xml\"\n        class=\"- map/topicref \">\n        <topicref format=\"dita\" href=\"/Content/replace_battery_xi1627_1_1.xml\"\n            class=\"- map/topicref \" />\n        <topicref href=\"/Content/insert_battery_xi1617_1_1.xml\" class=\"- map/topicref \" />\n    </topicref>\n    <topicref format=\"dita\" href=\"/Content/specifications_xi1634_1_1.xml\" class=\"- map/topicref \">\n        <topicref keyref=\"productSpec\" class=\"- map/topicref \" />\n    </topicref>\n    <topicref href=\"/Content/QuickStart_MP4_Handset_xi1641_1_1.xml\" format=\"dita\" platform=\"html\"\n        class=\"- map/topicref \" />\n    <topichead navtitle=\"Glossary\" toc=\"no\" class=\"+ map/topicref mapgroup-d/topichead \">\n        <glossref format=\"dita\" href=\"/Content/dual_band_xi1582_1_1.xml\" keys=\"quad_band\"\n            linking=\"normal\" print=\"yes\" search=\"yes\" toc=\"no\" type=\"glossentry\"\n            class=\"+ map/topicref glossref-d/glossref \" />\n        <glossref format=\"dita\" href=\"/Content/earphones_xi1584_1_1.xml\" keys=\"earphones\"\n            linking=\"normal\" print=\"yes\" search=\"no\" toc=\"no\" type=\"glossentry\"\n            class=\"+ map/topicref glossref-d/glossref \" />\n        <topicref format=\"dita\" href=\"/Content/nfc_antenna_xi1588_1_1.xml\" keys=\"nfc\"\n            class=\"- map/topicref \" />\n    </topichead>\n    <mapref format=\"ditamap\" href=\"/Content/subject_scheme_Atts_sample_xi1608_1_1.ditamap\"\n        class=\"+ map/topicref mapgroup-d/mapref \" />\n    <reltable toc=\"yes\" class=\"- map/reltable \">\n        <relheader class=\"- map/relheader \">\n            <relcolspec linking=\"sourceonly\" class=\"- map/relcolspec \" />\n            <relcolspec linking=\"targetonly\" class=\"- map/relcolspec \" />\n        </relheader>\n        <relrow class=\"- map/relrow \">\n            <relcell collection-type=\"family\" class=\"- map/relcell \">\n                <topicref format=\"dita\" href=\"/Content/getting_started_xi1615_1_1.xml\"\n                    class=\"- map/topicref \" />\n            </relcell>\n            <relcell collection-type=\"family\" class=\"- map/relcell \">\n                <topicref format=\"dita\" href=\"/Content/basics_xi1610_1_1.xml\"\n                    class=\"- map/topicref \" />\n            </relcell>\n        </relrow>\n        <relrow class=\"- map/relrow \">\n            <relcell class=\"- map/relcell \">\n                <topicref format=\"dita\" href=\"/Content/troubleshooting_xi1638_1_1.xml\"\n                    class=\"- map/topicref \" />\n            </relcell>\n            <relcell class=\"- map/relcell \">\n                <topicref format=\"dita\" href=\"/Content/specifications_xi1634_1_1.xml\"\n                    class=\"- map/topicref \" />\n            </relcell>\n        </relrow>\n        <relrow class=\"- map/relrow \">\n            <relcell class=\"- map/relcell \">\n                <topicref format=\"dita\" href=\"/Content/service_section_xi1633_1_1.xml\"\n                    class=\"- map/topicref \" />\n            </relcell>\n            <relcell collection-type=\"family\" class=\"- map/relcell \">\n                <topicref format=\"html\" href=\"http://www.bluestream.com/\" scope=\"external\"\n                    class=\"- map/topicref \">\n                    <topicmeta class=\"- map/topicmeta \">\n                        <navtitle class=\"- topic/navtitle \">Another Resource</navtitle>\n                    </topicmeta>\n                </topicref>\n            </relcell>\n        </relrow>\n        <relrow class=\"- map/relrow \" />\n        <relrow class=\"- map/relrow \" />\n    </reltable>\n</map>";
export declare const SUBJECT_SCHEME = "<subjectScheme xmlns:ditaarch=\"http://dita.oasis-open.org/architecture/2005/\" xml:lang=\"en-US\"\n    class=\"- map/map subjectScheme/subjectScheme \">\n    <!-- Define the values for the audience attribute -->\n    <subjectdef keys=\"users\" class=\"- map/topicref subjectScheme/subjectdef \">\n        <subjectdef keys=\"general\" class=\"- map/topicref subjectScheme/subjectdef \" />\n        <subjectdef keys=\"support\" class=\"- map/topicref subjectScheme/subjectdef \" />\n        <subjectdef keys=\"technician\" class=\"- map/topicref subjectScheme/subjectdef \" />\n    </subjectdef>\n    <!-- Define the values for the platform attribute -->\n    <subjectdef keys=\"output\" class=\"- map/topicref subjectScheme/subjectdef \">\n        <subjectdef keys=\"pdf\" class=\"- map/topicref subjectScheme/subjectdef \" />\n        <subjectdef keys=\"html\" class=\"- map/topicref subjectScheme/subjectdef \" />\n    </subjectdef>\n    <!--  Bind the attributes to the values  -->\n    <enumerationdef class=\"- map/topicref subjectScheme/enumerationdef \">\n        <attributedef name=\"audience\" class=\"- topic/data subjectScheme/attributedef \" />\n        <subjectdef keyref=\"users\" class=\"- map/topicref subjectScheme/subjectdef \" />\n    </enumerationdef>\n    <enumerationdef class=\"- map/topicref subjectScheme/enumerationdef \">\n        <attributedef name=\"platform\" class=\"- topic/data subjectScheme/attributedef \" />\n        <subjectdef keyref=\"output\" class=\"- map/topicref subjectScheme/subjectdef \" />\n    </enumerationdef>\n</subjectScheme>";
export declare const BEGIN_TOPIC_REF = "<topicref href=\"/Content/begin_xi1612_1_1.xml\" class=\"- map/topicref \" />";
export declare const GLOSSARY_TOPICHEAD = "<topichead navtitle=\"Glossary\" toc=\"no\" class=\"+ map/topicref mapgroup-d/topichead \">\n        <glossref format=\"dita\" href=\"/Content/dual_band_xi1582_1_1.xml\" keys=\"quad_band\"\n            linking=\"normal\" print=\"yes\" search=\"yes\" toc=\"no\" type=\"glossentry\"\n            class=\"+ map/topicref glossref-d/glossref \" />\n        <glossref format=\"dita\" href=\"/Content/earphones_xi1584_1_1.xml\" keys=\"earphones\"\n            linking=\"normal\" print=\"yes\" search=\"no\" toc=\"no\" type=\"glossentry\"\n            class=\"+ map/topicref glossref-d/glossref \" />\n        <topicref format=\"dita\" href=\"/Content/nfc_antenna_xi1588_1_1.xml\" keys=\"nfc\"\n            class=\"- map/topicref \" />\n    </topichead>";
