import { FileMeta } from "@bds/types";
import { DitamapUtils } from "./ditamap-utils";
import { createElementReference, TagNames } from "./ditamap-templates";
import { DitamapContext } from "./ditamap-tree";
import { v4 as uuidv4 } from "uuid";

type DitamapChildNode = MapReferenceNode | TopicReferenceNode | StructuralNode;

export interface DitamapNode {
  /** Unique identifier for the node */
  id: string;
  /** Display title for the node */
  title: string;
  /** Name of the containing map */
  containingMap: string | null;
  /** Path hierarchy to this node */
  mapPath: string[];
  /** Reference to the target resource */
  href: string | null;
  /** Child nodes */
  children: DitamapChildNode[];
  /** Type of the node */
  type: string;
  /** Context for the tree */
  context: DitamapContext;
  /** Reference to element inserted to map: mapref, part, topicref */
  refElement: HTMLElement | null;
  /** Reference to element resolved by the href: map, bookmap */
  structuralElement: HTMLElement | null;
  /**
   * Converts a file to a ref element that can be inserted into the map.
   */
  _fileToRefElement: (file: FileMeta) => HTMLElement;
  /**
   * Converts a node to a ref element that can be inserted into the map.
   */
  _nodeToRefElement: (node: DitamapChildNode) => HTMLElement;
  /**
   * Inserts a node from a file
   */
  insertNodeFromFile: (file: FileMeta) => void;
  /**
   * Inserts a node from another node
   */
  insertNodeFromNode: (node: DitamapChildNode) => void;
  copy: () => void;
  paste: () => void;
  remove: () => void;
  cut: () => void;
}

/**
 * Root node of the ditamap tree. E.g bookmap, map
 */
export class RootMapNode implements DitamapNode {
  href: string;
  containingMap: string | null;
  refElement: null;
  structuralElement: HTMLElement;
  mapPath: string[] = [];
  title: string;
  children: DitamapChildNode[] = [];
  id: string;
  context: DitamapContext;

  constructor({
    rootElement,
    rootMapName,
    context,
  }: {
    rootElement: HTMLElement;
    rootMapName: string;
    context: DitamapContext;
  }) {
    this.href = rootMapName;
    this.containingMap = null;
    this.refElement = null;
    this.structuralElement = rootElement;
    this.title = DitamapUtils.getMapTitle(this.structuralElement);
    this.context = context;
    this.id = this.structuralElement.getAttribute("data-id")!;
  }

  get type() {
    return this.structuralElement.tagName;
  }

  remove() {
    throw Error("Cannot remove root map");
  }

  cut() {
    throw Error("Cannot cut root map");
  }

  copy() {
    throw Error("Cannot copy root map");
  }

  paste() {
    if (this.context.buffer) {
      this.insertNodeFromNode(this.context.buffer);
    }
  }

  _insert(element: HTMLElement) {
    this.structuralElement.appendChild(element);
  }

  _fileToRefElement(file: FileMeta): HTMLElement {
    if (this.structuralElement.tagName == "bookmap") {
      if (file.rootElementName == "map") {
        return createElementReference("part", file.resLblId);
      } else {
        return createElementReference("chapter", file.resLblId);
      }
    }

    if (file.rootElementName == "map") {
      return createElementReference("mapref", file.resLblId);
    } else {
      return createElementReference("topicref", file.resLblId);
    }
  }

  _nodeToRefElement(node: DitamapChildNode): HTMLElement {
    if (this.structuralElement.tagName == "bookmap") {
      if (node instanceof MapReferenceNode) {
        return createElementReference("part", node.href);
      }
      if (node instanceof TopicReferenceNode) {
        return createElementReference("chapter", node.href);
      }

      let tagName = node.structuralElement.tagName as TagNames;
      return createElementReference(tagName, node.title);
    }

    if (node instanceof MapReferenceNode) {
      return createElementReference("mapref", node.href);
    }

    if (node instanceof TopicReferenceNode) {
      let tagName = node.refElement.tagName as TagNames;
      return createElementReference(tagName, node.href);
    }

    let tagName = node.structuralElement.tagName as TagNames;
    return createElementReference(tagName, node.title);
  }

  insertNodeFromFile(file: FileMeta) {
    if (file.rootElementName == "glossentry") {
      throw Error("Cannot insert glossentry into bookmap");
    }

    let refEl = this._fileToRefElement(file);
    this._insert(refEl);
    return;
  }

  insertNodeFromNode(node: DitamapChildNode) {
    let refEl = this._nodeToRefElement(node);
    this._insert(refEl);
    if (node instanceof TopicReferenceNode || node instanceof StructuralNode) {
      for (let child of node.children) {
        refEl.appendChild(node._nodeToRefElement(child));
      }
    }
  }
}

/**
 * References a ditamap - e.g part, mapref
 */
export class MapReferenceNode implements DitamapNode {
  href: string;
  containingMap: string;
  refElement: HTMLElement;
  structuralElement: HTMLElement;
  mapPath: string[] = [];
  children: DitamapChildNode[] = [];
  id: string;
  context: DitamapContext;

  constructor({
    refElement,
    structuralElement,
    containingMap,
    context,
  }: {
    refElement: HTMLElement;
    structuralElement: HTMLElement;
    containingMap: string;
    context: DitamapContext;
  }) {
    this.href = refElement.getAttribute("href")!;
    this.containingMap = containingMap;
    this.refElement = refElement;
    this.structuralElement = structuralElement;
    this.context = context;
    this.id = this.refElement.getAttribute("data-id")!;
  }

  get title() {
    return (
      this.context?.metadata.get(this.href)?.title ??
      DitamapUtils.getMapTitle(this.structuralElement)
    );
  }

  get type() {
    return (
      this.context?.metadata.get(this.href)?.rootElementName ??
      this.structuralElement.tagName
    );
  }

  cut() {
    this.context.buffer = this;
    this.context.cutId = this.id;
  }

  copy() {
    this.context.buffer = this;
  }

  remove() {
    this.refElement.remove();
  }

  paste() {
    if (this.context.buffer) {
      this.insertNodeFromNode(this.context.buffer);
    }

    if (this.context.cutId) {
      this.context.buffer.remove();
    }
  }

  _fileToRefElement(file: FileMeta): HTMLElement {
    if (file.rootElementName == "map") {
      return createElementReference("mapref", file.resLblId);
    }
    if (file.rootElementName == "glossentry") {
      return createElementReference("glossref", file.resLblId);
    } else {
      return createElementReference("topicref", file.resLblId);
    }
  }

  _nodeToRefElement(node: DitamapChildNode): HTMLElement {
    if (node instanceof MapReferenceNode) {
      return createElementReference("mapref", node.href);
    }

    if (node instanceof TopicReferenceNode) {
      let tagName = node.refElement.tagName as TagNames;
      return createElementReference(tagName, node.href);
    }

    let tagName = node.structuralElement.tagName as TagNames;
    return createElementReference(tagName, node.title);
  }

  _insert(element: HTMLElement) {
    this.structuralElement.appendChild(element);
  }

  insertNodeFromFile(file: FileMeta) {
    let refEl = this._fileToRefElement(file);
    this._insert(refEl);
  }

  insertNodeFromNode(node: DitamapChildNode) {
    let refEl = this._nodeToRefElement(node);
    this._insert(refEl);

    for (let child of node.children) {
      refEl.appendChild(node._nodeToRefElement(child));
    }
  }
}

/**
 * Refecences topics - e.g topicref, chapter, appendix, glossref.
 */
export class TopicReferenceNode implements DitamapNode {
  href: string;
  containingMap: string;
  refElement: HTMLElement;
  structuralElement: null = null;
  mapPath: string[] = [];
  children: DitamapChildNode[] = [];
  context: DitamapContext;
  id: string;

  constructor({
    refElement,
    containingMap,
    context,
  }: {
    refElement: HTMLElement;
    containingMap: string;
    context: DitamapContext;
  }) {
    this.href = refElement.getAttribute("href")!;
    this.containingMap = containingMap;
    this.refElement = refElement;
    this.context = context;
    this.id = this.refElement.getAttribute("data-id")!;
  }

  get title() {
    return (
      this.context?.metadata.get(this.href)?.title ??
      DitamapUtils.getMapTitle(this.refElement)
    );
  }

  get type() {
    return (
      this.context?.metadata.get(this.href)?.rootElementName ??
      this.refElement.tagName
    );
  }

  paste() {
    if (this.context.buffer) {
      this.insertNodeFromNode(this.context.buffer);
    }

    if (this.context.cutId) {
      this.context.buffer.remove();
    }
  }

  remove() {
    this.refElement.remove();
  }

  copy() {
    this.context.buffer = this;
  }

  cut() {
    this.context.buffer = this;
    this.context.cutId = this.id;
  }

  _fileToRefElement(file: FileMeta): HTMLElement {
    if (file.rootElementName == "map") {
      return createElementReference("mapref", file.resLblId);
    }
    if (file.rootElementName == "glossentry") {
      return createElementReference("glossref", file.resLblId);
    } else {
      return createElementReference("topicref", file.resLblId);
    }
  }

  _nodeToRefElement(node: DitamapChildNode): HTMLElement {
    if (node instanceof MapReferenceNode) {
      return createElementReference("mapref", node.href);
    }

    if (node instanceof TopicReferenceNode) {
      let tagName = node.refElement.tagName as TagNames;
      return createElementReference(tagName, node.href);
    }

    let tagName = node.structuralElement.tagName as TagNames;
    return createElementReference(tagName, node.title);
  }

  _insert(element: HTMLElement) {
    this.refElement.appendChild(element);
  }

  insertNodeFromFile(file: FileMeta) {
    let refEl = this._fileToRefElement(file);
    this.refElement.appendChild(refEl);
  }

  insertNodeFromNode(node: DitamapChildNode) {
    let refEl = this._nodeToRefElement(node);
    this._insert(refEl);

    for (let child of node.children) {
      refEl.appendChild(node._nodeToRefElement(child));
    }
  }
}

/**
 * Provides structure to the map. E.g topichead, topicgroup
 */
export class StructuralNode implements DitamapNode {
  containingMap: string;
  refElement: null = null;
  structuralElement: HTMLElement;
  mapPath: string[] = [];
  children: DitamapChildNode[] = [];
  id: string;
  context: DitamapContext;

  constructor(
    structuralElement: HTMLElement,
    containingMap: string,
    context: DitamapContext
  ) {
    this.containingMap = containingMap;
    this.structuralElement = structuralElement;
    this.id = this.structuralElement.getAttribute("data-id")!;
    this.context = context;
  }

  get title() {
    return (
      this.structuralElement.getAttribute("navtitle") ??
      this.structuralElement.tagName
    );
  }

  get type() {
    return this.structuralElement.tagName;
  }

  get href() {
    return this.containingMap + "#" + this.title;
  }

  paste() {
    if (this.context.buffer) {
      this.insertNodeFromNode(this.context.buffer);
    }

    if (this.context.cutId) {
      this.context.buffer.remove();
    }
  }

  copy() {
    this.context.buffer = this;
  }

  cut() {
    this.context.buffer = this;
    this.context.cutId = this.id;
  }

  remove() {
    this.structuralElement.remove();
  }

  _fileToRefElement(file: FileMeta): HTMLElement {
    if (file.rootElementName == "map") {
      return createElementReference("mapref", file.resLblId);
    }
    if (file.rootElementName == "glossentry") {
      return createElementReference("glossref", file.resLblId);
    } else {
      return createElementReference("topicref", file.resLblId);
    }
  }

  insertNodeFromNode(node: DitamapChildNode) {
    let refEl = this._nodeToRefElement(node);
    this.structuralElement.appendChild(refEl);

    for (let child of node.children) {
      node.insertNodeFromNode(child);
    }
  }
  _nodeToRefElement(node: DitamapChildNode): HTMLElement {
    if (node instanceof MapReferenceNode) {
      return createElementReference("mapref", node.href);
    }

    if (node instanceof TopicReferenceNode) {
      let tagName = node.refElement.tagName as TagNames;
      return createElementReference(tagName, node.href);
    }

    let tagName = node.structuralElement.tagName as TagNames;
    return createElementReference(tagName);
  }

  insertNodeFromFile(file: FileMeta) {
    let refEl = this._fileToRefElement(file);
    this.structuralElement.appendChild(refEl);
  }
}
