{"version": 3, "file": "insert-ref_test.js", "sourceRoot": "", "sources": ["../../../src/test/ditamap-tree/insert-ref_test.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,gBAAgB,EAChB,WAAW,EACX,kBAAkB,GACnB,MAAM,kCAAkC,CAAC;AAE1C,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAC1C,OAAO,EACL,gBAAgB,EAEhB,iBAAiB,EACjB,kBAAkB,GACnB,MAAM,iBAAiB,CAAC;AACzB,OAAO,KAAK,QAAQ,MAAM,kBAAkB,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAsB,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAEjG,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;QACzC,IAAI,UAAU,GAAG,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QACtD,IAAI,iBAAiB,GAAG,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;QAC5D,IAAI,MAAM,GAAG,IAAI,gBAAgB,CAAC;YAChC,UAAU,EAAE,UAAU;YACtB,iBAAiB,EAAE,iBAAiB;YACpC,aAAa,EAAE,4CAA4C;SAC5D,CAAC,CAAC;QAEH,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,aAAa,CACjD,UAAU,QAAQ,CAAC,UAAU,CAAC,QAAQ,IAAI,CAC1C,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAC5C,IAAI,UAAU,GAAG,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QACtD,IAAI,iBAAiB,GAAG,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;QAC5D,IAAI,MAAM,GAAG,IAAI,gBAAgB,CAAC;YAChC,UAAU,EAAE,UAAU;YACtB,iBAAiB,EAAE,iBAAiB;YACpC,aAAa,EAAE,4CAA4C;SAC5D,CAAC,CAAC;QAEH,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QAEnD,IAAI,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,aAAa,CACjD,UAAU,QAAQ,CAAC,cAAc,CAAC,QAAQ,IAAI,CAC9C,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAC1C,QAAQ,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACnC,IAAI,UAAU,GAAG,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QACtD,IAAI,iBAAiB,GAAG,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;QAC5D,IAAI,MAAM,GAAG,IAAI,gBAAgB,CAAC;YAChC,UAAU,EAAE,UAAU;YACtB,iBAAiB,EAAE,iBAAiB;YACpC,aAAa,EAAE,4CAA4C;SAC5D,CAAC,CAAC;QAEH,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAChD,IAAI,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,aAAa,CACjD,UAAU,QAAQ,CAAC,WAAW,CAAC,QAAQ,IAAI,CAC3C,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC1C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC9E,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,yDAAyD;AACzD,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACrC,IAAI,iBAAiB,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QAC3D,IAAI,MAAM,GAAG,IAAI,WAAW,CAAC;YAC3B,WAAW,EAAE,iBAAiB;YAC9B,WAAW,EAAE,4CAA4C;SAC1D,CAAC,CAAC;QACH,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAChD,IAAI,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,aAAa,CACjD,8DAA8D,CAC9D,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACxC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAC1C,qDAAqD,CACtD,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;QACzC,IAAI,iBAAiB,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QAC3D,IAAI,MAAM,GAAG,IAAI,WAAW,CAAC;YAC3B,WAAW,EAAE,iBAAiB;YAC9B,WAAW,EAAE,4CAA4C;SAC1D,CAAC,CAAC;QACH,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,aAAa,CACjD,wCAAwC,CACxC,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAC1C,+BAA+B,CAChC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qBAAqB,EAAE,GAAG,EAAE;QAC7B,IAAI,iBAAiB,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QAC3D,IAAI,MAAM,GAAG,IAAI,WAAW,CAAC;YAC3B,WAAW,EAAE,iBAAiB;YAC9B,WAAW,EAAE,4CAA4C;SAC1D,CAAC,CAAC;QACH,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;IAC9E,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;QACvC,IAAI,UAAU,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;QACnD,IAAI,MAAM,GAAG,IAAI,kBAAkB,CAAC;YAClC,UAAU,EAAE,UAAU;YACtB,aAAa,EAAE,4CAA4C;YAC3D,OAAO,EAAE;gBACP,SAAS,EAAE,IAAI,GAAG,EAAE;gBACpB,QAAQ,EAAE,IAAI,GAAG,EAAE;gBACnB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;aACb;SACF,CAAC,CAAC;QACH,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAChD,IAAI,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,aAAa,CAC1C,UAAU,QAAQ,CAAC,WAAW,CAAC,QAAQ,IAAI,CAC3C,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC1C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC9E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAC1C,IAAI,UAAU,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;QACnD,IAAI,MAAM,GAAG,IAAI,kBAAkB,CAAC;YAClC,UAAU,EAAE,UAAU;YACtB,aAAa,EAAE,4CAA4C;YAC3D,OAAO,EAAE;gBACP,SAAS,EAAE,IAAI,GAAG,EAAE;gBACpB,QAAQ,EAAE,IAAI,GAAG,EAAE;gBACnB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;aACb;SACF,CAAC,CAAC;QACH,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,aAAa,CAC1C,UAAU,QAAQ,CAAC,UAAU,CAAC,QAAQ,IAAI,CAC1C,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import {\r\n  MapReferenceNode,\r\n  RootMapNode,\r\n  TopicReferenceNode,\r\n} from \"../../lib/jsDitamap/ditamap-node\";\r\n\r\nimport { expect } from \"@open-wc/testing\";\r\nimport {\r\n  createXmlElement,\r\n  MAP_DITA20,\r\n  MAP_SUBMAP_PHONES,\r\n  PART_SUBMAP_PHONES,\r\n} from \"./data/elements\";\r\nimport * as metadata from \"./files/metadata\";\r\nimport { BEGIN_TOPIC_REF, GLOSSARY_TOPICHEAD, PHONES_1_BOOKMAP } from \"./files/phones_1_bookmap\";\r\n\r\ndescribe(\"Map Reference Parent\", () => {\r\n  it(\"Inserts a concept as a topicref\", () => {\r\n    let refElement = createXmlElement(PART_SUBMAP_PHONES);\r\n    let structuralElement = createXmlElement(MAP_SUBMAP_PHONES);\r\n    let parent = new MapReferenceNode({\r\n      refElement: refElement,\r\n      structuralElement: structuralElement,\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n    });\r\n\r\n    parent.insertNodeFromFile(metadata.BEGIN_FILE);\r\n    let target = parent.structuralElement.querySelector(\r\n      `[href='${metadata.BEGIN_FILE.resLblId}']`\r\n    )!;\r\n\r\n    expect(target.tagName).to.equal(\"topicref\");\r\n    expect(target.getAttribute(\"href\")).to.equal(metadata.BEGIN_FILE.resLblId);\r\n  });\r\n\r\n  it(\"Inserts a glossentry as a topicref\", () => {\r\n    let refElement = createXmlElement(PART_SUBMAP_PHONES);\r\n    let structuralElement = createXmlElement(MAP_SUBMAP_PHONES);\r\n    let parent = new MapReferenceNode({\r\n      refElement: refElement,\r\n      structuralElement: structuralElement,\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n    });\r\n\r\n    parent.insertNodeFromFile(metadata.DUAL_BAND_FILE);\r\n\r\n    let target = parent.structuralElement.querySelector(\r\n      `[href='${metadata.DUAL_BAND_FILE.resLblId}']`\r\n    )!;\r\n    expect(target.tagName).to.equal(\"glossref\");\r\n    expect(target.getAttribute(\"href\")).to.equal(\r\n      metadata.DUAL_BAND_FILE.resLblId\r\n    );\r\n  });\r\n\r\n  it(\"Inserts a map as a mapref\", () => {\r\n    let refElement = createXmlElement(PART_SUBMAP_PHONES);\r\n    let structuralElement = createXmlElement(MAP_SUBMAP_PHONES);\r\n    let parent = new MapReferenceNode({\r\n      refElement: refElement,\r\n      structuralElement: structuralElement,\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n    });\r\n\r\n    parent.insertNodeFromFile(metadata.DITA20_FILE);\r\n    let target = parent.structuralElement.querySelector(\r\n      `[href='${metadata.DITA20_FILE.resLblId}']`\r\n    )!;\r\n    expect(target.tagName).to.equal(\"mapref\");\r\n    expect(target.getAttribute(\"href\")).to.equal(metadata.DITA20_FILE.resLblId);\r\n  });\r\n});\r\n\r\n/** Needs further tests as to where it can be inserted */\r\ndescribe(\"Bookmap Parent\", () => {\r\n  it(\"Inserts a ditamap as a part\", () => {\r\n    let structuralElement = createXmlElement(PHONES_1_BOOKMAP);\r\n    let parent = new RootMapNode({\r\n      rootElement: structuralElement,\r\n      rootMapName: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n    });\r\n    parent.insertNodeFromFile(metadata.DITA20_FILE);\r\n    let target = parent.structuralElement.querySelector(\r\n      \"[href='/Content/dita20_quickstart_guide_xi1683_1_1.ditamap']\"\r\n    )!;\r\n    expect(target.tagName).to.equal(\"part\");\r\n    expect(target.getAttribute(\"href\")).to.equal(\r\n      \"/Content/dita20_quickstart_guide_xi1683_1_1.ditamap\"\r\n    );\r\n  });\r\n\r\n  it(\"Inserts a topicref as a chapter\", () => {\r\n    let structuralElement = createXmlElement(PHONES_1_BOOKMAP);\r\n    let parent = new RootMapNode({\r\n      rootElement: structuralElement,\r\n      rootMapName: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n    });\r\n    parent.insertNodeFromFile(metadata.BEGIN_FILE);\r\n    let target = parent.structuralElement.querySelector(\r\n      \"[href='/Content/begin_xi1612_1_1.xml']\"\r\n    )!;\r\n    expect(target.tagName).to.equal(\"chapter\");\r\n    expect(target.getAttribute(\"href\")).to.equal(\r\n      \"/Content/begin_xi1612_1_1.xml\"\r\n    );\r\n  });\r\n\r\n  it(\"Blocks a glossentry\", () => {\r\n    let structuralElement = createXmlElement(PHONES_1_BOOKMAP);\r\n    let parent = new RootMapNode({\r\n      rootElement: structuralElement,\r\n      rootMapName: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n    });\r\n    expect(() => parent.insertNodeFromFile(metadata.DUAL_BAND_FILE)).to.throw();\r\n  });\r\n});\r\n\r\ndescribe(\"Topic Reference Parent\", () => {\r\n  it(\"Inserts a ditamap as a mapref\", () => {\r\n    let refElement = createXmlElement(BEGIN_TOPIC_REF);\r\n    let parent = new TopicReferenceNode({\r\n      refElement: refElement,\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      context: {\r\n        workspace: new Map(),\r\n        metadata: new Map(),\r\n        dirty: new Set(),\r\n        cutId: null,\r\n        buffer: null,\r\n      },\r\n    });\r\n    parent.insertNodeFromFile(metadata.DITA20_FILE);\r\n    let target = parent.refElement.querySelector(\r\n      `[href='${metadata.DITA20_FILE.resLblId}']`\r\n    )!;\r\n    expect(target.tagName).to.equal(\"mapref\");\r\n    expect(target.getAttribute(\"href\")).to.equal(metadata.DITA20_FILE.resLblId);\r\n  });\r\n\r\n  it(\"Inserts a topicref as a topicref\", () => {\r\n    let refElement = createXmlElement(BEGIN_TOPIC_REF);\r\n    let parent = new TopicReferenceNode({\r\n      refElement: refElement,\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      context: {\r\n        workspace: new Map(),\r\n        metadata: new Map(),\r\n        dirty: new Set(),\r\n        cutId: null,\r\n        buffer: null,\r\n      },\r\n    });\r\n    parent.insertNodeFromFile(metadata.BEGIN_FILE);\r\n    let target = parent.refElement.querySelector(\r\n      `[href='${metadata.BEGIN_FILE.resLblId}']`\r\n    )!;\r\n    expect(target.tagName).to.equal(\"topicref\");\r\n    expect(target.getAttribute(\"href\")).to.equal(metadata.BEGIN_FILE.resLblId);\r\n  });\r\n});\r\n"]}