{"version": 3, "file": "delete_test.js", "sourceRoot": "", "sources": ["../../../src/test/ditamap-tree/delete_test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AACrH,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,eAAe,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AAChH,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAE1C,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;IACvB,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACjC,IAAI,EAAE,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QAC5C,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAE9B,IAAI,IAAI,GAAG,IAAI,WAAW,CAAC;YACzB,WAAW,EAAE,EAAE;YACf,WAAW,EAAE,4CAA4C;SAC1D,CAAC,CAAC;QAEH,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;IACzC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,EAAE,CAAC,gBAAgB,EAAE,GAAG,EAAE;QACxB,IAAI,EAAE,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAC;QACzC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAE9B,IAAI,IAAI,GAAG,IAAI,gBAAgB,CAAC;YAC9B,UAAU,EAAE,EAAE;YACd,iBAAiB,EAAE,EAAE;YACrB,aAAa,EAAE,4CAA4C;YAC3D,OAAO,EAAE;gBACP,SAAS,EAAE,IAAI,GAAG,EAAE;gBACpB,QAAQ,EAAE,IAAI,GAAG,EAAE;gBACnB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;aACb;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC;IACnD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAC1B,IAAI,EAAE,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;QAC3C,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAE9B,IAAI,IAAI,GAAG,IAAI,kBAAkB,CAAC;YAChC,UAAU,EAAE,EAAE;YACd,aAAa,EAAE,4CAA4C;YAC3D,OAAO,EAAE;gBACP,SAAS,EAAE,IAAI,GAAG,EAAE;gBACpB,QAAQ,EAAE,IAAI,GAAG,EAAE;gBACnB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;aACb;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC;IACnD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAC3B,IAAI,EAAE,GAAG,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QAC9C,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAE9B,IAAI,IAAI,GAAG,IAAI,cAAc,CAAC,EAAE,EAAE,2CAA2C,CAAC,CAAC;QAC/E,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,MAAM,CAAC,IAAI,EAAE,iBAAiB,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC;IAC1D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { MapReferenceNode, RootMapNode, StructuralNode, TopicReferenceNode } from \"../../lib/jsDitamap/ditamap-node\";\r\nimport { createXmlElement } from \"./data/elements\";\r\nimport { BEGIN_TOPIC_REF, GLOSSARY_TOPICHEAD, PHONES_1_BOOKMAP, SUBMAP_PHONES } from \"./files/phones_1_bookmap\";\r\nimport { expect } from \"@open-wc/testing\";\r\n\r\ndescribe(\"Rootmap\", () => {\r\n  it(\"Blocks rootmap deletion\", () => {\r\n    let el = createXmlElement(PHONES_1_BOOKMAP);\r\n    document.body.appendChild(el);\r\n\r\n    let node = new RootMapNode({\r\n      rootElement: el,\r\n      rootMapName: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n    });\r\n\r\n    expect(() => node.remove()).to.throw();\r\n  });\r\n});\r\n\r\ndescribe(\"Map Reference\", () => {\r\n  it(\"Deletes mapref\", () => {\r\n    let el = createXmlElement(SUBMAP_PHONES);\r\n    document.body.appendChild(el);\r\n\r\n    let node = new MapReferenceNode({\r\n      refElement: el,\r\n      structuralElement: el,\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      context: {\r\n        workspace: new Map(),\r\n        metadata: new Map(),\r\n        dirty: new Set(),\r\n        cutId: null,\r\n        buffer: null,\r\n      },\r\n    });\r\n\r\n    node.remove();\r\n    expect(node?.refElement.isConnected).to.be.false;\r\n  });\r\n});\r\n\r\ndescribe(\"Topic Reference\", () => {\r\n  it(\"Deletes topicref\", () => {\r\n    let el = createXmlElement(BEGIN_TOPIC_REF);\r\n    document.body.appendChild(el);\r\n\r\n    let node = new TopicReferenceNode({\r\n      refElement: el,\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      context: {\r\n        workspace: new Map(),\r\n        metadata: new Map(),\r\n        dirty: new Set(),\r\n        cutId: null,\r\n        buffer: null,\r\n      },\r\n    });\r\n\r\n    node.remove();\r\n    expect(node?.refElement.isConnected).to.be.false;\r\n  });\r\n});\r\n\r\ndescribe(\"Structural Element\", () => {\r\n  it(\"Deletes topichead\", () => {\r\n    let el = createXmlElement(GLOSSARY_TOPICHEAD);\r\n    document.body.appendChild(el);\r\n\r\n    let node = new StructuralNode(el, \"/Content/Submap_Phones_xi1609_1_1.ditamap\");\r\n    node.remove();\r\n    expect(node?.structuralElement.isConnected).to.be.false;\r\n  });\r\n});\r\n"]}