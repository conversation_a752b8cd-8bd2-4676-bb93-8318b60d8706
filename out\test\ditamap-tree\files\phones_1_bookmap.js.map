{"version": 3, "file": "phones_1_bookmap.js", "sourceRoot": "", "sources": ["../../../../src/test/ditamap-tree/files/phones_1_bookmap.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,gBAAgB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;WAqDrB,CAAC;AAEZ,MAAM,CAAC,MAAM,aAAa,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAwGtB,CAAC;AAER,MAAM,CAAC,MAAM,cAAc,GAAG;;;;;;;;;;;;;;;;;;;;;;iBAsBb,CAAC;AAElB,MAAM,CAAC,MAAM,eAAe,GAAG,2EAA2E,CAAC;AAC3G,MAAM,CAAC,MAAM,kBAAkB,GAAG;;;;;;;;;iBASjB,CAAC", "sourcesContent": ["export const PHONES_1_BOOKMAP = `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<!DOCTYPE bookmap PUBLIC \"-//OASIS//DTD DITA BookMap//EN\" \"/SysSchema/dita/dtd/bookmap/dtd/bookmap.dtd\">\r\n<bookmap xmlns:ditaarch=\"http://dita.oasis-open.org/architecture/2005/\"\r\n\tid=\"xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0\" xml:lang=\"en-US\"\r\n\tclass=\"- map/map bookmap/bookmap \">\r\n\t<booktitle class=\"- topic/title bookmap/booktitle \">\r\n\t\t<mainbooktitle class=\"- topic/ph bookmap/mainbooktitle \">\r\n\t\t\t<?xm-replace_text Main Book Title?>Phone 1 User Guide</mainbooktitle>\r\n\t</booktitle>\r\n\r\n\t<bookmeta class=\"- map/topicmeta bookmap/bookmeta \">\r\n\t\t<bookid class=\"- topic/data bookmap/bookid \">\r\n\t\t\t<bookpartno class=\"- topic/data bookmap/bookpartno \">\r\n\t\t\t\t<?xm-replace_text Book Part Number?>\r\n\t\t\t</bookpartno>\r\n\t\t\t<isbn class=\"- topic/data bookmap/isbn \">\r\n\t\t\t\t<?xm-replace_text ISBN Number?>\r\n\t\t\t</isbn>\r\n\t\t</bookid>\r\n\t\t<bookrights class=\"- topic/data bookmap/bookrights \">\r\n\t\t\t<copyrfirst class=\"- topic/data bookmap/copyrfirst \">\r\n\t\t\t\t<year class=\"- topic/ph bookmap/year \">\r\n\t\t\t\t\t<?xm-replace_text First Year?>\r\n\t\t\t\t</year>\r\n\t\t\t</copyrfirst>\r\n\t\t\t<copyrlast class=\"- topic/data bookmap/copyrlast \">\r\n\t\t\t\t<year class=\"- topic/ph bookmap/year \">\r\n\t\t\t\t\t<?xm-replace_text Last Year?>\r\n\t\t\t\t</year>\r\n\t\t\t</copyrlast>\r\n\t\t\t<bookowner class=\"- topic/data bookmap/bookowner \">\r\n\t\t\t\t<person class=\"- topic/data bookmap/person \">\r\n\t\t\t\t\t<?xm-replace_text Copyright owner?>\r\n\t\t\t\t</person>\r\n\t\t\t</bookowner>\r\n\t\t</bookrights>\r\n\t</bookmeta>\r\n\t<frontmatter class=\"- map/topicref bookmap/frontmatter \">\r\n\t\t<booklists class=\"- map/topicref bookmap/booklists \">\r\n\t\t\t<toc class=\"- map/topicref bookmap/toc \" />\r\n\t\t</booklists>\r\n\t\t<keydef keys=\"productSpec\" href=\"/Content/phone_1_spec_xi1622_1_1.xml\"\r\n\t\t\tclass=\"+ map/topicref mapgroup-d/keydef \" />\r\n\t\t<keydef href=\"/Content/phone1_small_xi1596_1_1.jpg\" format=\"png\" keys=\"phoneImage\"\r\n\t\t\tclass=\"+ map/topicref mapgroup-d/keydef \" />\r\n\t</frontmatter>\r\n\t<part href=\"/Content/submap_phones_xi1609_1_1.ditamap\" format=\"ditamap\"\r\n\t\tclass=\"- map/topicref bookmap/part \" />\r\n\t<backmatter class=\"- map/topicref bookmap/backmatter \">\r\n\t\t<booklists class=\"- map/topicref bookmap/booklists \">\r\n\t\t\t<indexlist class=\"- map/topicref bookmap/indexlist \" />\r\n\t\t</booklists>\r\n\t</backmatter>\r\n</bookmap>`;\r\n\r\nexport const SUBMAP_PHONES = `<map xmlns:ditaarch=\"http://dita.oasis-open.org/architecture/2005/\"\r\n    id=\"map_1C03AB61C63B4FA6A95F36368EFD4772\" title=\"Submap Phones\" xml:lang=\"en-US\"\r\n    class=\"- map/map \">\r\n    <topicref format=\"dita\" href=\"/Content/begin_xi1612_1_1.xml\" class=\"- map/topicref \">\r\n        <topicref format=\"dita\" href=\"/Content/getting_started_xi1615_1_1.xml\"\r\n            class=\"- map/topicref \" />\r\n        <topicref format=\"dita\" href=\"/Content/insert_SIMcard_xi1616_1_1.xml\"\r\n            class=\"- map/topicref \" />\r\n        <topicref format=\"dita\" href=\"/Content/charge_battery_xi1613_1_1.xml\"\r\n            class=\"- map/topicref \" />\r\n    </topicref>\r\n    <topicref format=\"dita\" href=\"/Content/basics_xi1610_1_1.xml\" class=\"- map/topicref \">\r\n        <topicref format=\"dita\" href=\"/Content/overview_handset_xi1621_1_1.xml\"\r\n            class=\"- map/topicref \" />\r\n        <topicref format=\"dita\" href=\"/Content/make_and_receive_calls_xi1619_1_1.xml\"\r\n            class=\"- map/topicref \">\r\n            <topicref format=\"dita\" href=\"/Content/make_calls_xi1620_1_1.xml\"\r\n                class=\"- map/topicref \" />\r\n            <topicref format=\"dita\" href=\"/Content/receive_call_xi1625_1_1.xml\"\r\n                class=\"- map/topicref \" />\r\n            <topicref format=\"dita\" href=\"/Content/reject_call_xi1626_1_1.xml\"\r\n                class=\"- map/topicref \" />\r\n        </topicref>\r\n        <topicref format=\"dita\" href=\"/Content/retrieve_voicemail_xi1631_1_1.xml\"\r\n            class=\"- map/topicref \" />\r\n        <topicref href=\"/Content/taking_photos_xi1637_1_1.xml\" class=\"- map/topicref \">\r\n            <topicref format=\"dita\" href=\"/Content/take_photos_xi1636_1_1.xml\"\r\n                class=\"- map/topicref \" />\r\n            <topicref href=\"/Content/take_front-facing_photo_xi1635_1_1.xml\" class=\"- map/topicref \" />\r\n        </topicref>\r\n        <topicref format=\"dita\" href=\"/Content/listen%20_music_xi1618_1_1.xml\"\r\n            class=\"- map/topicref \" />\r\n        <topicref href=\"/Content/exchange_data_xi1614_1_1.xml\" class=\"- map/topicref \" />\r\n        <topicref format=\"dita\" href=\"/Content/reset_phone_xi1630_1_1.xml\" class=\"- map/topicref \" />\r\n    </topicref>\r\n    <topicref format=\"dita\" href=\"/Content/troubleshooting_xi1638_1_1.xml\" class=\"- map/topicref \">\r\n        <topicref href=\"/Content/screen_frozen_xi1632_1_1.xml\" class=\"- map/topicref \" />\r\n        <topicref format=\"dita\" href=\"/Content/battery_drain_xi1611_1_1.xml\" class=\"- map/topicref \" />\r\n    </topicref>\r\n    <topicref audience=\"technician\" format=\"dita\" href=\"/Content/service_section_xi1633_1_1.xml\"\r\n        class=\"- map/topicref \">\r\n        <topicref format=\"dita\" href=\"/Content/replace_battery_xi1627_1_1.xml\"\r\n            class=\"- map/topicref \" />\r\n        <topicref href=\"/Content/insert_battery_xi1617_1_1.xml\" class=\"- map/topicref \" />\r\n    </topicref>\r\n    <topicref format=\"dita\" href=\"/Content/specifications_xi1634_1_1.xml\" class=\"- map/topicref \">\r\n        <topicref keyref=\"productSpec\" class=\"- map/topicref \" />\r\n    </topicref>\r\n    <topicref href=\"/Content/QuickStart_MP4_Handset_xi1641_1_1.xml\" format=\"dita\" platform=\"html\"\r\n        class=\"- map/topicref \" />\r\n    <topichead navtitle=\"Glossary\" toc=\"no\" class=\"+ map/topicref mapgroup-d/topichead \">\r\n        <glossref format=\"dita\" href=\"/Content/dual_band_xi1582_1_1.xml\" keys=\"quad_band\"\r\n            linking=\"normal\" print=\"yes\" search=\"yes\" toc=\"no\" type=\"glossentry\"\r\n            class=\"+ map/topicref glossref-d/glossref \" />\r\n        <glossref format=\"dita\" href=\"/Content/earphones_xi1584_1_1.xml\" keys=\"earphones\"\r\n            linking=\"normal\" print=\"yes\" search=\"no\" toc=\"no\" type=\"glossentry\"\r\n            class=\"+ map/topicref glossref-d/glossref \" />\r\n        <topicref format=\"dita\" href=\"/Content/nfc_antenna_xi1588_1_1.xml\" keys=\"nfc\"\r\n            class=\"- map/topicref \" />\r\n    </topichead>\r\n    <mapref format=\"ditamap\" href=\"/Content/subject_scheme_Atts_sample_xi1608_1_1.ditamap\"\r\n        class=\"+ map/topicref mapgroup-d/mapref \" />\r\n    <reltable toc=\"yes\" class=\"- map/reltable \">\r\n        <relheader class=\"- map/relheader \">\r\n            <relcolspec linking=\"sourceonly\" class=\"- map/relcolspec \" />\r\n            <relcolspec linking=\"targetonly\" class=\"- map/relcolspec \" />\r\n        </relheader>\r\n        <relrow class=\"- map/relrow \">\r\n            <relcell collection-type=\"family\" class=\"- map/relcell \">\r\n                <topicref format=\"dita\" href=\"/Content/getting_started_xi1615_1_1.xml\"\r\n                    class=\"- map/topicref \" />\r\n            </relcell>\r\n            <relcell collection-type=\"family\" class=\"- map/relcell \">\r\n                <topicref format=\"dita\" href=\"/Content/basics_xi1610_1_1.xml\"\r\n                    class=\"- map/topicref \" />\r\n            </relcell>\r\n        </relrow>\r\n        <relrow class=\"- map/relrow \">\r\n            <relcell class=\"- map/relcell \">\r\n                <topicref format=\"dita\" href=\"/Content/troubleshooting_xi1638_1_1.xml\"\r\n                    class=\"- map/topicref \" />\r\n            </relcell>\r\n            <relcell class=\"- map/relcell \">\r\n                <topicref format=\"dita\" href=\"/Content/specifications_xi1634_1_1.xml\"\r\n                    class=\"- map/topicref \" />\r\n            </relcell>\r\n        </relrow>\r\n        <relrow class=\"- map/relrow \">\r\n            <relcell class=\"- map/relcell \">\r\n                <topicref format=\"dita\" href=\"/Content/service_section_xi1633_1_1.xml\"\r\n                    class=\"- map/topicref \" />\r\n            </relcell>\r\n            <relcell collection-type=\"family\" class=\"- map/relcell \">\r\n                <topicref format=\"html\" href=\"http://www.bluestream.com/\" scope=\"external\"\r\n                    class=\"- map/topicref \">\r\n                    <topicmeta class=\"- map/topicmeta \">\r\n                        <navtitle class=\"- topic/navtitle \">Another Resource</navtitle>\r\n                    </topicmeta>\r\n                </topicref>\r\n            </relcell>\r\n        </relrow>\r\n        <relrow class=\"- map/relrow \" />\r\n        <relrow class=\"- map/relrow \" />\r\n    </reltable>\r\n</map>`;\r\n\r\nexport const SUBJECT_SCHEME = `<subjectScheme xmlns:ditaarch=\"http://dita.oasis-open.org/architecture/2005/\" xml:lang=\"en-US\"\r\n    class=\"- map/map subjectScheme/subjectScheme \">\r\n    <!-- Define the values for the audience attribute -->\r\n    <subjectdef keys=\"users\" class=\"- map/topicref subjectScheme/subjectdef \">\r\n        <subjectdef keys=\"general\" class=\"- map/topicref subjectScheme/subjectdef \" />\r\n        <subjectdef keys=\"support\" class=\"- map/topicref subjectScheme/subjectdef \" />\r\n        <subjectdef keys=\"technician\" class=\"- map/topicref subjectScheme/subjectdef \" />\r\n    </subjectdef>\r\n    <!-- Define the values for the platform attribute -->\r\n    <subjectdef keys=\"output\" class=\"- map/topicref subjectScheme/subjectdef \">\r\n        <subjectdef keys=\"pdf\" class=\"- map/topicref subjectScheme/subjectdef \" />\r\n        <subjectdef keys=\"html\" class=\"- map/topicref subjectScheme/subjectdef \" />\r\n    </subjectdef>\r\n    <!--  Bind the attributes to the values  -->\r\n    <enumerationdef class=\"- map/topicref subjectScheme/enumerationdef \">\r\n        <attributedef name=\"audience\" class=\"- topic/data subjectScheme/attributedef \" />\r\n        <subjectdef keyref=\"users\" class=\"- map/topicref subjectScheme/subjectdef \" />\r\n    </enumerationdef>\r\n    <enumerationdef class=\"- map/topicref subjectScheme/enumerationdef \">\r\n        <attributedef name=\"platform\" class=\"- topic/data subjectScheme/attributedef \" />\r\n        <subjectdef keyref=\"output\" class=\"- map/topicref subjectScheme/subjectdef \" />\r\n    </enumerationdef>\r\n</subjectScheme>`;\r\n\r\nexport const BEGIN_TOPIC_REF = `<topicref href=\"/Content/begin_xi1612_1_1.xml\" class=\"- map/topicref \" />`;\r\nexport const GLOSSARY_TOPICHEAD = `<topichead navtitle=\"Glossary\" toc=\"no\" class=\"+ map/topicref mapgroup-d/topichead \">\r\n        <glossref format=\"dita\" href=\"/Content/dual_band_xi1582_1_1.xml\" keys=\"quad_band\"\r\n            linking=\"normal\" print=\"yes\" search=\"yes\" toc=\"no\" type=\"glossentry\"\r\n            class=\"+ map/topicref glossref-d/glossref \" />\r\n        <glossref format=\"dita\" href=\"/Content/earphones_xi1584_1_1.xml\" keys=\"earphones\"\r\n            linking=\"normal\" print=\"yes\" search=\"no\" toc=\"no\" type=\"glossentry\"\r\n            class=\"+ map/topicref glossref-d/glossref \" />\r\n        <topicref format=\"dita\" href=\"/Content/nfc_antenna_xi1588_1_1.xml\" keys=\"nfc\"\r\n            class=\"- map/topicref \" />\r\n    </topichead>`;\r\n"]}