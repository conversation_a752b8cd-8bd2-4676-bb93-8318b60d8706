import { MapReferenceNode, RootMapNode, StructuralNode, TopicReferenceNode } from "../../lib/jsDitamap/ditamap-node";
import { createXmlElement } from "./data/elements";
import { BEGIN_TOPIC_REF, GLOSSARY_TOPICHEAD, PHONES_1_BOOKMAP, SUBMAP_PHONES } from "./files/phones_1_bookmap";
import { expect } from "@open-wc/testing";

describe("Rootmap", () => {
  it("Blocks rootmap deletion", () => {
    let el = createXmlElement(PHONES_1_BOOKMAP);
    document.body.appendChild(el);

    let node = new RootMapNode({
      rootElement: el,
      rootMapName: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
    });

    expect(() => node.remove()).to.throw();
  });
});

describe("Map Reference", () => {
  it("Deletes mapref", () => {
    let el = createXmlElement(SUBMAP_PHONES);
    document.body.appendChild(el);

    let node = new MapReferenceNode({
      refElement: el,
      structuralElement: el,
      containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      context: {
        workspace: new Map(),
        metadata: new Map(),
        dirty: new Set(),
        cutId: null,
        buffer: null,
      },
    });

    node.remove();
    expect(node?.refElement.isConnected).to.be.false;
  });
});

describe("Topic Reference", () => {
  it("Deletes topicref", () => {
    let el = createXmlElement(BEGIN_TOPIC_REF);
    document.body.appendChild(el);

    let node = new TopicReferenceNode({
      refElement: el,
      containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      context: {
        workspace: new Map(),
        metadata: new Map(),
        dirty: new Set(),
        cutId: null,
        buffer: null,
      },
    });

    node.remove();
    expect(node?.refElement.isConnected).to.be.false;
  });
});

describe("Structural Element", () => {
  it("Deletes topichead", () => {
    let el = createXmlElement(GLOSSARY_TOPICHEAD);
    document.body.appendChild(el);

    let node = new StructuralNode(el, "/Content/Submap_Phones_xi1609_1_1.ditamap");
    node.remove();
    expect(node?.structuralElement.isConnected).to.be.false;
  });
});
