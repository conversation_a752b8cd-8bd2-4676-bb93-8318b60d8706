{"version": 3, "file": "cut-copy-paste_test.js", "sourceRoot": "", "sources": ["../../../src/test/ditamap-tree/cut-copy-paste_test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAC1C,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AACtE,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAE3D,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;IACtB,EAAE,CAAC,IAAI,CAAC,6BAA6B,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IACjD,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;QAClC,IAAI,EAAE,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;QAC3C,IAAI,IAAI,GAAG,IAAI,kBAAkB,CAAC;YAChC,UAAU,EAAE,EAAE;YACd,aAAa,EAAE,4CAA4C;YAC3D,OAAO,EAAE;gBACP,SAAS,EAAE,IAAI,GAAG,EAAE;gBACpB,QAAQ,EAAE,IAAI,GAAG,EAAE;gBACnB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;aACb;SACF,CAAC,CAAC;QACH,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC1B,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACvC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACtD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,IAAI,CAAC,wBAAwB,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC,CAAC", "sourcesContent": ["import { expect } from \"@open-wc/testing\";\r\nimport { createXmlElement } from \"./data/elements\";\r\nimport { TopicReferenceNode } from \"../../lib/jsDitamap/ditamap-node\";\r\nimport { BEGIN_TOPIC_REF } from \"./files/phones_1_bookmap\";\r\n\r\ndescribe(\"_clone\", () => {\r\n  it.skip(\"Clones a structural element\", () => {});\r\n  it(\"Clones a topic reference\", () => {\r\n    let el = createXmlElement(BEGIN_TOPIC_REF);\r\n    let node = new TopicReferenceNode({\r\n      refElement: el,\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      context: {\r\n        workspace: new Map(),\r\n        metadata: new Map(),\r\n        dirty: new Set(),\r\n        cutId: null,\r\n        buffer: null,\r\n      },\r\n    });\r\n    let clone = node._clone();\r\n    expect(clone.id).to.not.equal(node.id);\r\n    expect(clone.refElement.tagName).to.equal(\"topicref\");\r\n    expect(clone.refElement).to.not.equal(node.refElement);\r\n  });\r\n  it.skip(\"Clones a map reference\", () => {});\r\n});\r\n"]}