import "bds-tree";
import { LitElement, TemplateResult } from "lit";
import { DitamapTree } from "../lib/jsDitamap/ditamap-tree";
import { DitamapNode } from "lib/jsDitamap/ditamap-node";
import "./wex-ditamap-tree-toolbar.ts";
import "./wex-ditamap-icon.ts";
import { BDSTree } from "bds-tree";
import { DitamapCommandManager } from "lib/jsDitamap/ditamap-command-manager";
export declare class WexDitamapTree extends LitElement {
    treeConfig: any;
    commandManager?: DitamapCommandManager;
    tree: DitamapTree | null;
    root: DitamapNode | null;
    rootMap: string;
    selectedNode: DitamapNode | null;
    status: "success" | "error" | "loading";
    treeEl?: BDSTree<DitamapNode>;
    constructor();
    connectedCallback(): Promise<void>;
    updated(): void;
    handleCut(): Promise<void>;
    handleCopy(): void;
    handleInsert(): Promise<void>;
    handleRemove(): Promise<void>;
    handleTreeClick(e: any): void;
    /**
     * Controls the display of nodes in the tree
     */
    nodeRenderer(node: WexDitamapNode): TemplateResult;
    render(): TemplateResult<1>;
}
interface WexDitamapNode extends DitamapNode {
    key: string;
    label: string;
}
export {};
