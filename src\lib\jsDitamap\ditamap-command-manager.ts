import { DitamapTree } from "./ditamap-tree";
import { DitamapNode } from "./ditamap-node";
import { FileMeta } from "@bds/types";
import { notifyUser } from "lib/util";

export class DitamapCommandManager {
  history: Command[] = [];

  constructor(
    private tree: DitamapTree,
    private setRoot: (root: DitamapNode) => void
  ) {}

  async execute(command: Command): Promise<boolean> {
    try {
      command.execute();
      this.history.push(command);
      this.unsetCut(command);
      await this.tree.rebuildTree();
      this.setRoot(this.tree.getRoot()!);
      return true
    } catch (err) {
      notifyUser("warn", err);
      return false;
    }
  }

  /**
   * Cancel the cut operation if the command is not a cut
   */
  unsetCut(command: Command) {
    if (!(command instanceof CutCommand)) {
      this.tree.getRoot()!.context.cutId = null;
    }
    this.tree.getRoot()!.context.cutId = null;
  }

  undo() {
    this.history.pop()?.undo();
  }
}

export interface Command {
  execute(): void;
  undo(): void;
}

export class CutCommand implements Command {
  constructor(private node: DitamapNode) {}

  execute() {
    this.node.cut();
  }

  undo() {
    console.log("Undo");
  }
}

export class InsertCommand implements Command {
  constructor(
    private node: DitamapNode,
    private file: FileMeta
  ) {}

  execute() {
    if (this.file.rootElementName === "bookmap") {
      throw Error("Cannot insert bookmap into bookmap");
    }
    
    this.node.insertNodeFromFile(this.file);
  }

  undo() {
    console.log("Undo");
  }
}

export class RemoveCommand implements Command {
  constructor(private node: DitamapNode) {}

  execute() {
    this.node.remove();
  }

  undo() {
    console.log("Delete");
  }
}
