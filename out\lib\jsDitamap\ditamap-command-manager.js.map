{"version": 3, "file": "ditamap-command-manager.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-command-manager.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AAEtC,MAAM,OAAO,qBAAqB;IAGhC,YACU,IAAiB,EACjB,OAAoC;QADpC,SAAI,GAAJ,IAAI,CAAa;QACjB,YAAO,GAAP,OAAO,CAA6B;QAJ9C,YAAO,GAAc,EAAE,CAAC;IAKrB,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,OAAgB;QAC5B,IAAI,CAAC;YACH,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACvB,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAG,CAAC,CAAC;YACnC,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,OAAgB;QACvB,IAAI,CAAC,CAAC,OAAO,YAAY,UAAU,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAG,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;QAC5C,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAG,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;IAC5C,CAAC;IAED,IAAI;QACF,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC;IAC7B,CAAC;CACF;AAOD,MAAM,OAAO,UAAU;IACrB,YAAoB,IAAiB;QAAjB,SAAI,GAAJ,IAAI,CAAa;IAAG,CAAC;IAEzC,OAAO;QACL,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;IAClB,CAAC;IAED,IAAI;QACF,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;CACF;AAED,MAAM,OAAO,aAAa;IACxB,YACU,IAAiB,EACjB,IAAc;QADd,SAAI,GAAJ,IAAI,CAAa;QACjB,SAAI,GAAJ,IAAI,CAAU;IACrB,CAAC;IAEJ,OAAO;QACL,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YAC5C,MAAM,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,IAAI;QACF,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;CACF;AAED,MAAM,OAAO,aAAa;IACxB,YAAoB,IAAiB;QAAjB,SAAI,GAAJ,IAAI,CAAa;IAAG,CAAC;IAEzC,OAAO;QACL,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;IACrB,CAAC;IAED,IAAI;QACF,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACxB,CAAC;CACF", "sourcesContent": ["import { DitamapTree } from \"./ditamap-tree\";\r\nimport { DitamapNode } from \"./ditamap-node\";\r\nimport { FileMeta } from \"@bds/types\";\r\nimport { notifyUser } from \"lib/util\";\r\n\r\nexport class DitamapCommandManager {\r\n  history: Command[] = [];\r\n\r\n  constructor(\r\n    private tree: DitamapTree,\r\n    private setRoot: (root: DitamapNode) => void\r\n  ) {}\r\n\r\n  async execute(command: Command): Promise<boolean> {\r\n    try {\r\n      command.execute();\r\n      this.history.push(command);\r\n      this.unsetCut(command);\r\n      await this.tree.rebuildTree();\r\n      this.setRoot(this.tree.getRoot()!);\r\n      return true\r\n    } catch (err) {\r\n      notifyUser(\"warn\", err);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cancel the cut operation if the command is not a cut\r\n   */\r\n  unsetCut(command: Command) {\r\n    if (!(command instanceof CutCommand)) {\r\n      this.tree.getRoot()!.context.cutId = null;\r\n    }\r\n    this.tree.getRoot()!.context.cutId = null;\r\n  }\r\n\r\n  undo() {\r\n    this.history.pop()?.undo();\r\n  }\r\n}\r\n\r\nexport interface Command {\r\n  execute(): void;\r\n  undo(): void;\r\n}\r\n\r\nexport class CutCommand implements Command {\r\n  constructor(private node: DitamapNode) {}\r\n\r\n  execute() {\r\n    this.node.cut();\r\n  }\r\n\r\n  undo() {\r\n    console.log(\"Undo\");\r\n  }\r\n}\r\n\r\nexport class InsertCommand implements Command {\r\n  constructor(\r\n    private node: DitamapNode,\r\n    private file: FileMeta\r\n  ) {}\r\n\r\n  execute() {\r\n    if (this.file.rootElementName === \"bookmap\") {\r\n      throw Error(\"Cannot insert bookmap into bookmap\");\r\n    }\r\n    \r\n    this.node.insertNodeFromFile(this.file);\r\n  }\r\n\r\n  undo() {\r\n    console.log(\"Undo\");\r\n  }\r\n}\r\n\r\nexport class RemoveCommand implements Command {\r\n  constructor(private node: DitamapNode) {}\r\n\r\n  execute() {\r\n    this.node.remove();\r\n  }\r\n\r\n  undo() {\r\n    console.log(\"Delete\");\r\n  }\r\n}\r\n"]}