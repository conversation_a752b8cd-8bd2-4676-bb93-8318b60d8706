import { LitElement, css, html } from "lit";
import { customElement, property } from "lit/decorators.js";
import "@ui5/webcomponents/dist/Button.js";

@customElement("wex-ditamap-tree-toolbar")
class WexDitamapTreeToolbar extends LitElement {
  @property({ type: Boolean })
  hasSelection: boolean = false;

  _emit(event: string) {
    this.dispatchEvent(
      new CustomEvent(event, { bubbles: true, composed: true })
    );
  }

  render() {
    return html`
      <ul class="toolbar">
        <li>
          <button
            ?disabled="${!this.hasSelection}"
            @click=${() => this._emit("insert")}
          >
            <iron-icon icon="vaadin:file-add"></iron-icon>
          </button>
        </li>
        <li>
          <button
            ?disabled="${!this.hasSelection}"
            @click=${() => this._emit("cut")}
          >
            <iron-icon icon="vaadin:scissors"></iron-icon>
          </button>
        </li>
        <li>
          <button
            ?disabled="${!this.hasSelection}"
            @click=${() => this._emit("copy")}
          >
            <iron-icon icon="vaadin:copy-o"></iron-icon>
          </button>
        </li>
        <li>
          <button
            ?disabled="${!this.hasSelection}"
            @click=${() => this._emit("remove")}
          >
            <iron-icon icon="vaadin:trash"></iron-icon>
          </button>
        </li>
      </ul>
    `;
  }

  static styles = css`
    .toolbar {
      list-style: none;
      padding: 0;
      display: flex;
      flex-direction: row;
    }
    .toolbar button {
      background: none;
      border: 1px solid;
    }

    iron-icon {
      --iron-icon-width: 1rem;
    }
  `;
}
