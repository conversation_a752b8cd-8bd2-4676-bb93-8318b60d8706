{"version": 3, "file": "wex-ditamap-tree.js", "sourceRoot": "", "sources": ["../../src/components/wex-ditamap-tree.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,UAAU,CAAC;AAClB,OAAO,EAAE,UAAU,EAAkB,IAAI,EAAE,MAAM,KAAK,CAAC;AACvD,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AAEnE,OAAO,+BAA+B,CAAC;AACvC,OAAO,uBAAuB,CAAC;AAE/B,OAAO,EAAE,QAAQ,EAAE,MAAM,6BAA6B,CAAC;AACvD,OAAO,EACL,UAAU,EACV,qBAAqB,EACrB,aAAa,EACb,aAAa,GACd,MAAM,uCAAuC,CAAC;AAGxC,IAAM,cAAc,GAApB,MAAM,cAAe,SAAQ,UAAU;IAe5C;QACE,KAAK,EAAE,CAAC;QAbV,SAAI,GAAuB,IAAI,CAAC;QAEhC,SAAI,GAAuB,IAAI,CAAC;QAIhC,iBAAY,GAAuB,IAAI,CAAC;QAExC,WAAM,GAAoC,SAAS,CAAC;QAMlD,IAAI,CAAC,UAAU,GAAG;YAChB,SAAS,EAAE,OAAO;YAClB,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE,UAAU;YACrB,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,SAAS;YAClB,WAAW,EAAE,IAAI;YACjB,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;SAC3C,CAAC;QACF,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,iBAAiB;QACrB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAG,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B,CAAC;IAED,OAAO;QACL,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACnD,IAAI,CAAC,cAAc,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE;gBAClE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACnB,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS;QACb,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED,UAAU;QACR,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,CAAC,cAAc,EAAE,OAAO,CAChC,IAAI,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,eAAe,CAAC,CACtD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YACtF,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAC3B,CAAC;QACH,CAAC;IACH,CAAC;IAED,eAAe,CAAC,CAAC;QACf,IAAI,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACxB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC3B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,IAAoB;QAC/B,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,IAAI,CAAA;;;;;;gBAMC,QAAQ,CAAC;YACf,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,KAAK,IAAI,CAAC,GAAG;SAC3C,CAAC;;iCAEuB,IAAI,EAAE,IAAI;UACjC,IAAI,CAAC,KAAK;QACZ,CAAC;IACP,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAA,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS;YACrC,CAAC,CAAC,IAAI,CAAA,uBAAuB;YAC7B,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO;gBACtB,CAAC,CAAC,IAAI,CAAA,kBAAkB;gBACxB,CAAC,CAAC,IAAI,CAAA;+BACiB,CAAC,CAAC,IAAI,CAAC,YAAY;yBACzB,IAAI,CAAC,YAAY;yBACjB,IAAI,CAAC,YAAY;sBACpB,IAAI,CAAC,SAAS;uBACb,IAAI,CAAC,UAAU;;;2BAGX,IAAI,CAAC,YAAY,EAAE,KAAK,IAAI,MAAM;gBAC7C,IAAI,CAAC,YAAY,EAAE,EAAE;;;2BAGV,IAAI,CAAC,eAAe;wBACvB,IAAI,CAAC,UAAU;sBACjB,IAAI,CAAC,IAAI;yBACN,EAAE,CAAC;IAC1B,CAAC;CACF,CAAA;AA5HC;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;4CACK;AAEhC;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;+CACX;AAEhB;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;oDACa;AAExC;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8CACyB;AAEpD;IADC,KAAK,CAAC,UAAU,CAAC;8CACY;AAbnB,cAAc;IAD1B,aAAa,CAAC,kBAAkB,CAAC;GACrB,cAAc,CAiI1B;;AAED,MAAM,SAAS,GAAG;IAChB,SAAS,EAAE,gCAAgC;IAC3C,WAAW,EAAE,CAAC,CAAC;IACf,UAAU,EAAE,CAAC,CAAC;IACd,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,WAAW;IACpB,QAAQ,EAAE,IAAI;IACd,UAAU,EAAE,QAAQ;IACpB,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,UAAU;IACpB,IAAI,EAAE,WAAW;IACjB,QAAQ,EAAE,+BAA+B;CAC1C,CAAC;AACF,2CAA2C;AAC3C,MAAM,QAAQ,GAAG;IACf,SAAS,EAAE,YAAY;IACvB,WAAW,EAAE,CAAC,UAAU;IACxB,UAAU,EAAE,CAAC,UAAU;IACvB,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,WAAW;IACpB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;IACf,UAAU,EAAE,QAAQ;IACpB,QAAQ,EAAE,UAAU;IACpB,IAAI,EAAE,iCAAiC;IACvC,WAAW,EAAE,MAAM;IACnB,QAAQ,EAAE,qDAAqD;IAC/D,SAAS,EAAE,6DAA6D;IACxE,eAAe,EAAE,KAAK;IACtB,KAAK,EAAE,iCAAiC;IACxC,aAAa,EAAE,sBAAsB;IACrC,UAAU,EAAE,eAAe;IAC3B,MAAM,EAAE,CAAC;CACV,CAAC;AAEF,MAAM,YAAY,GAAG;IACnB,SAAS,EAAE,4BAA4B;IACvC,WAAW,EAAE,CAAC,UAAU;IACxB,UAAU,EAAE,CAAC,UAAU;IACvB,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,WAAW;IACpB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,sBAAsB;IAChC,YAAY,EAAE,kCAAkC;IAChD,SAAS,EAAE,YAAY;IACvB,UAAU,EAAE,gBAAgB;IAC5B,QAAQ,EAAE,UAAU;IACpB,IAAI,EAAE,wBAAwB;IAC9B,WAAW,EAAE,MAAM;IACnB,QAAQ,EAAE,4CAA4C;IACtD,SAAS,EAAE,oDAAoD;IAC/D,eAAe,EAAE,SAAS;IAC1B,KAAK,EAAE,oBAAoB;IAC3B,aAAa,EAAE,sBAAsB;IACrC,UAAU,EAAE,eAAe;IAC3B,MAAM,EAAE,CAAC;CACV,CAAC;AAEF,MAAM,eAAe,GAAG;IACtB,SAAS,EAAE,sDAAsD;IACjE,WAAW,EAAE,CAAC,UAAU;IACxB,UAAU,EAAE,CAAC,UAAU;IACvB,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,WAAW;IACpB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;IACf,UAAU,EAAE,QAAQ;IACpB,QAAQ,EAAE,UAAU;IACpB,IAAI,EAAE,aAAa;IACnB,WAAW,EAAE,MAAM;IACnB,QAAQ,EAAE,iCAAiC;IAC3C,SAAS,EAAE,kDAAkD;IAC7D,eAAe,EAAE,YAAY;IAC7B,KAAK,EAAE,aAAa;IACpB,aAAa,EAAE,sBAAsB;IACrC,UAAU,EAAE,eAAe;IAC3B,MAAM,EAAE,CAAC;CACV,CAAC", "sourcesContent": ["import \"bds-tree\";\r\nimport { LitElement, TemplateResult, html } from \"lit\";\r\nimport { DitamapTree } from \"../lib/jsDitamap/ditamap-tree\";\r\nimport { customElement, property, query } from \"lit/decorators.js\";\r\nimport { DitamapNode } from \"lib/jsDitamap/ditamap-node\";\r\nimport \"./wex-ditamap-tree-toolbar.ts\";\r\nimport \"./wex-ditamap-icon.ts\";\r\nimport { BDSTree } from \"bds-tree\";\r\nimport { classMap } from \"lit/directives/class-map.js\";\r\nimport {\r\n  CutCommand,\r\n  DitamapCommandManager,\r\n  InsertCommand,\r\n  RemoveCommand,\r\n} from \"lib/jsDitamap/ditamap-command-manager\";\r\n\r\n@customElement(\"wex-ditamap-tree\")\r\nexport class WexDitamapTree extends LitElement {\r\n  treeConfig: any;\r\n  commandManager?: DitamapCommandManager;\r\n  tree: DitamapTree | null = null;\r\n  @property({ type: Object })\r\n  root: DitamapNode | null = null;\r\n  @property({ type: String })\r\n  rootMap: string;\r\n  @property({ type: Object })\r\n  selectedNode: DitamapNode | null = null;\r\n  @property({ type: String })\r\n  status: \"success\" | \"error\" | \"loading\" = \"loading\";\r\n  @query(\"bds-tree\")\r\n  treeEl?: BDSTree<DitamapNode>;\r\n\r\n  constructor() {\r\n    super();\r\n    this.treeConfig = {\r\n      labelPath: \"title\",\r\n      idPath: \"id\",\r\n      childPath: \"children\",\r\n      showRoot: true,\r\n      iconSet: \"ditamap\",\r\n      hasUnselect: true,\r\n      nodeRenderer: this.nodeRenderer.bind(this),\r\n    };\r\n    this.rootMap = \"\";\r\n  }\r\n\r\n  async connectedCallback() {\r\n    super.connectedCallback();\r\n    console.log(\"MAP: \", this.rootMap);\r\n    this.tree = new DitamapTree(this.rootMap);\r\n    this.status = \"loading\";\r\n    await this.tree.load();\r\n    this.root = this.tree.getRoot()!;\r\n    this.status = \"success\";\r\n  }\r\n\r\n  updated() {\r\n    if (!this.commandManager && this.tree && this.root) {\r\n      this.commandManager = new DitamapCommandManager(this.tree, (root) => {\r\n        this.root = root;\r\n      });\r\n    }\r\n  }\r\n\r\n  async handleCut() {\r\n    if (this.selectedNode) {\r\n      await this.commandManager?.execute(new CutCommand(this.selectedNode));\r\n    }\r\n  }\r\n\r\n  handleCopy() {\r\n    if (this.selectedNode) {\r\n      console.log(\"Copy: \", this.selectedNode);\r\n    }\r\n  }\r\n\r\n  async handleInsert() {\r\n    if (this.selectedNode) {\r\n      await this.commandManager?.execute(\r\n        new InsertCommand(this.selectedNode, TEMP_GLOSSENTRY)\r\n      );\r\n    }\r\n  }\r\n\r\n  async handleRemove() {\r\n    if (this.selectedNode) {\r\n      let result = await this.commandManager?.execute(new RemoveCommand(this.selectedNode));\r\n      if (result) {\r\n        this.selectedNode = null;\r\n      }\r\n    }\r\n  }\r\n\r\n  handleTreeClick(e) {\r\n    if (e.detail.isSelected) {\r\n      this.selectedNode = e.detail.node;\r\n    } else {\r\n      this.selectedNode = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Controls the display of nodes in the tree\r\n   */\r\n  nodeRenderer(node: WexDitamapNode): TemplateResult {\r\n    if (node.context?.cutId === node.key) {\r\n      console.log(\"Do we have context?: \", node);\r\n    }\r\n\r\n    return html` <style>\r\n        .is-cut {\r\n          opacity: 0.25;\r\n        }\r\n      </style>\r\n      <span\r\n        class=${classMap({\r\n          \"is-cut\": node.context?.cutId === node.key,\r\n        })}\r\n      >\r\n        <wex-ditamap-icon icon=${node?.type}></wex-ditamap-icon>\r\n        ${node.label}</span\r\n      >`;\r\n  }\r\n\r\n  render() {\r\n    return html` ${this.status == \"loading\"\r\n      ? html`<div>Loading...</div>`\r\n      : this.status == \"error\"\r\n        ? html`<div>Error</div>`\r\n        : html` <wex-ditamap-tree-toolbar\r\n              .hasSelection=\"${!!this.selectedNode}\"\r\n              @insert=\"${this.handleInsert}\"\r\n              @remove=\"${this.handleRemove}\"\r\n              @cut=\"${this.handleCut}\"\r\n              @copy=\"${this.handleCopy}\"\r\n            ></wex-ditamap-tree-toolbar>\r\n            <span\r\n              >Selected: ${this.selectedNode?.title || \"None\"}\r\n              ${this.selectedNode?.id}</span\r\n            >\r\n            <bds-tree\r\n              @treeClick=${this.handleTreeClick}\r\n              .config=${this.treeConfig}\r\n              .root=${this.root}\r\n            ></bds-tree>`}`;\r\n  }\r\n}\r\n\r\nconst TEMP_FILE = {\r\n  ditaClass: \"- topic/topic concept/concept \",\r\n  imageHeight: -1,\r\n  imageWidth: -1,\r\n  isXml: true,\r\n  kbResId: \"Xdfv_1612\",\r\n  lblSeqId: 1612,\r\n  lockorType: \"NoLock\",\r\n  lineageId: 1612,\r\n  mimeType: \"text/xml\",\r\n  name: \"begin.xml\",\r\n  resLblId: \"/Content/begin_xi1609_1_1.xml\",\r\n};\r\n// Content/submap_phones_xi1606_1_1.ditamap\r\nconst TEMP_MAP = {\r\n  ditaClass: \"- map/map \",\r\n  imageHeight: -2147483648,\r\n  imageWidth: -2147483648,\r\n  isXml: true,\r\n  kbResId: \"Xdfv_1680\",\r\n  lblSeqId: 1680,\r\n  lineageId: 1680,\r\n  lockorType: \"NoLock\",\r\n  mimeType: \"text/xml\",\r\n  name: \"dita20_quickstart_guide.ditamap\",\r\n  permissions: \"RUDP\",\r\n  resLblId: \"/Content/dita20_quickstart_guide_xi1680_1_1.ditamap\",\r\n  resPathId: \"/Content/sample/dita/dita20/dita20_quickstart_guide.ditamap\",\r\n  rootElementName: \"map\",\r\n  title: \"DITA 2.0 Phone Quickstart Guide\",\r\n  verCreateDate: \"2025-08-22T18:50:12Z\",\r\n  verCreator: \"administrator\",\r\n  verNum: 1,\r\n};\r\n\r\nconst TEMP_BOOKMAP = {\r\n  ditaClass: \"- map/map bookmap/bookmap \",\r\n  imageHeight: -2147483648,\r\n  imageWidth: -2147483648,\r\n  isXml: true,\r\n  kbResId: \"Xdfv_1574\",\r\n  lblSeqId: 1574,\r\n  lineageId: 1574,\r\n  lockDate: \"2025-09-02T13:25:51Z\",\r\n  lockLocation: \"wex:cc:room:-8630300732938674629\",\r\n  lockOwner: \"concurrent\",\r\n  lockorType: \"RoomConcurrent\",\r\n  mimeType: \"text/xml\",\r\n  name: \"Phone1_Bookmap.ditamap\",\r\n  permissions: \"RUDP\",\r\n  resLblId: \"/Content/Phone1_Bookmap_xi1574_1_1.ditamap\",\r\n  resPathId: \"/Content/sample/dita/phones/Phone1_Bookmap.ditamap\",\r\n  rootElementName: \"bookmap\",\r\n  title: \"Phone 1 User Guide\",\r\n  verCreateDate: \"2025-08-22T18:49:40Z\",\r\n  verCreator: \"administrator\",\r\n  verNum: 1,\r\n};\r\n\r\nconst TEMP_GLOSSENTRY = {\r\n  ditaClass: \"- topic/topic concept/concept glossentry/glossentry \",\r\n  imageHeight: -2147483648,\r\n  imageWidth: -2147483648,\r\n  isXml: true,\r\n  kbResId: \"Xdfv_1578\",\r\n  lblSeqId: 1578,\r\n  lineageId: 1578,\r\n  lockorType: \"NoLock\",\r\n  mimeType: \"text/xml\",\r\n  name: \"charger.xml\",\r\n  permissions: \"RUDP\",\r\n  resLblId: \"/Content/charger_xi1578_1_1.xml\",\r\n  resPathId: \"/Content/sample/dita/phones/glossary/charger.xml\",\r\n  rootElementName: \"glossentry\",\r\n  title: \"charger.xml\",\r\n  verCreateDate: \"2025-08-22T18:49:40Z\",\r\n  verCreator: \"administrator\",\r\n  verNum: 1,\r\n};\r\n\r\ninterface WexDitamapNode extends DitamapNode {\r\n  key: string;\r\n  label: string;\r\n}\r\n"]}