import { DitamapTree } from "./ditamap-tree";
import { DitamapNode } from "./ditamap-node";
import { FileMeta } from "@bds/types";
export declare class DitamapCommandManager {
    private tree;
    private setRoot;
    history: Command[];
    constructor(tree: DitamapTree, setRoot: (root: DitamapNode) => void);
    execute(command: Command): Promise<boolean>;
    /**
     * Cancel the cut operation if the command is not a cut
     */
    unsetCut(command: Command): void;
    undo(): void;
}
export interface Command {
    execute(): void;
    undo(): void;
}
export declare class CutCommand implements Command {
    private node;
    constructor(node: DitamapNode);
    execute(): void;
    undo(): void;
}
export declare class InsertCommand implements Command {
    private node;
    private file;
    constructor(node: DitamapNode, file: FileMeta);
    execute(): void;
    undo(): void;
}
export declare class RemoveCommand implements Command {
    private node;
    constructor(node: DitamapNode);
    execute(): void;
    undo(): void;
}
