import { expect } from "@open-wc/testing";
import { createXmlElement } from "./data/elements";
import { TopicReferenceNode } from "../../lib/jsDitamap/ditamap-node";
import { BEGIN_TOPIC_REF } from "./files/phones_1_bookmap";
describe("_clone", () => {
    it.skip("Clones a structural element", () => { });
    it("Clones a topic reference", () => {
        let el = createXmlElement(BEGIN_TOPIC_REF);
        let node = new TopicReferenceNode({
            refElement: el,
            containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
            context: {
                workspace: new Map(),
                metadata: new Map(),
                dirty: new Set(),
                cutId: null,
                buffer: null,
            },
        });
        let clone = node._clone();
        expect(clone.id).to.not.equal(node.id);
        expect(clone.refElement.tagName).to.equal("topicref");
        expect(clone.refElement).to.not.equal(node.refElement);
    });
    it.skip("Clones a map reference", () => { });
});
//# sourceMappingURL=cut-copy-paste_test.js.map