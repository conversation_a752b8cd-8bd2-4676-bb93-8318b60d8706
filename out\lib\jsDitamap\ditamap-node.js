import { DitamapUtils } from "./ditamap-utils";
import { createRefElement } from "./ditamap-templates";
/**
 * Root node of the ditamap tree. E.g bookmap, map
 */
export class RootMapNode {
    constructor({ rootElement, rootMapName, context, }) {
        this.mapPath = [];
        this.children = [];
        this.href = rootMapName;
        this.containingMap = null;
        this.refElement = null;
        this.structuralElement = rootElement;
        this.title = DitamapUtils.getMapTitle(this.structuralElement);
        this.context = context;
        this.id = this.structuralElement.getAttribute("data-id");
    }
    get type() {
        return this.structuralElement.tagName;
    }
    remove() {
        throw Error("Cannot remove root map");
    }
    cut() {
        throw Error("Cannot cut root map");
    }
    copy() {
        throw Error("Cannot copy root map");
    }
    _insert(element) {
        this.structuralElement.appendChild(element);
    }
    _fileToRefElement(file) {
        if (this.structuralElement.tagName == "bookmap") {
            if (file.rootElementName == "map") {
                return createRefElement("part", file);
            }
            else {
                return createRefElement("chapter", file);
            }
        }
        if (file.rootElementName == "map") {
            return createRefElement("mapref", file);
        }
        else {
            return createRefElement("topicref", file);
        }
    }
    insertNodeFromFile(file) {
        if (file.rootElementName == "glossentry") {
            throw Error("Cannot insert glossentry into bookmap");
        }
        let refEl = this._fileToRefElement(file);
        this._insert(refEl);
        return;
    }
}
/**
 * References a ditamap - e.g part, mapref
 */
export class MapReferenceNode {
    constructor({ refElement, structuralElement, containingMap, context, }) {
        this.mapPath = [];
        this.children = [];
        this.href = refElement.getAttribute("href");
        this.containingMap = containingMap;
        this.refElement = refElement;
        this.structuralElement = structuralElement;
        this.context = context;
        this.id = this.structuralElement.getAttribute("data-id");
    }
    get title() {
        return (this.context?.metadata.get(this.href)?.title ??
            DitamapUtils.getMapTitle(this.structuralElement));
    }
    get type() {
        return (this.context?.metadata.get(this.href)?.rootElementName ??
            this.structuralElement.tagName);
    }
    cut() {
        this.context.buffer = this;
        this.context.cutId = this.id;
        console.log(this.context);
    }
    copy() {
        this.context.buffer = this._clone();
    }
    _clone() {
        let newRefEl = this.refElement.cloneNode(true);
        newRefEl.setAttribute("data-id", uuidv4());
        let newNode = new MapReferenceNode({
            refElement: newRefEl,
            structuralElement: this.structuralElement,
            containingMap: this.containingMap,
            context: this.context,
        });
        for (let child of this.children) {
            newNode.children.push(child.copy());
        }
        return newNode;
    }
    remove() {
        this.refElement.remove();
    }
    _fileToRefElement(file) {
        if (file.rootElementName == "map") {
            return createRefElement("mapref", file);
        }
        if (file.rootElementName == "glossentry") {
            return createRefElement("glossref", file);
        }
        else {
            return createRefElement("topicref", file);
        }
    }
    insertNodeFromFile(file) {
        let refEl = this._fileToRefElement(file);
        this.structuralElement.appendChild(refEl);
    }
}
/**
 * Refecences topics - e.g topicref, chapter, appendix, glossref.
 */
export class TopicReferenceNode {
    constructor({ refElement, containingMap, context, }) {
        this.structuralElement = null;
        this.mapPath = [];
        this.children = [];
        this.href = refElement.getAttribute("href");
        this.containingMap = containingMap;
        this.refElement = refElement;
        this.context = context;
        this.id = this.refElement.getAttribute("data-id");
    }
    get title() {
        return (this.context?.metadata.get(this.href)?.title ??
            DitamapUtils.getMapTitle(this.refElement));
    }
    get type() {
        return (this.context?.metadata.get(this.href)?.rootElementName ??
            this.refElement.tagName);
    }
    _fileToRefElement(file) {
        if (file.rootElementName == "map") {
            return createRefElement("mapref", file);
        }
        if (file.rootElementName == "glossentry") {
            return createRefElement("glossref", file);
        }
        else {
            return createRefElement("topicref", file);
        }
    }
    insertNodeFromFile(file) {
        let refEl = this._fileToRefElement(file);
        this.refElement.appendChild(refEl);
    }
    remove() {
        this.refElement.remove();
    }
    _clone() {
        let newRefEl = this.refElement.cloneNode(true);
        newRefEl.setAttribute("data-id", uuidv4());
        let newNode = new TopicReferenceNode({
            refElement: newRefEl,
            containingMap: this.containingMap,
            context: this.context,
        });
        for (let child of this.children) {
            newNode.children.push(child.copy());
        }
        return newNode;
    }
    copy() {
        this.context.buffer = this._clone();
    }
    cut() {
        this.context.buffer = this;
        this.context.cutId = this.id;
        console.log(this.context);
    }
}
/**
 * Provides structure to the map. E.g topichead, topicgroup
 */
export class StructuralNode {
    constructor(structuralElement, containingMap, context) {
        this.refElement = null;
        this.mapPath = [];
        this.children = [];
        this.containingMap = containingMap;
        this.structuralElement = structuralElement;
        this.id = this.structuralElement.getAttribute("data-id");
        this.context = context;
    }
    get title() {
        return (this.structuralElement.getAttribute("navtitle") ??
            this.structuralElement.tagName);
    }
    get type() {
        return this.structuralElement.tagName;
    }
    get href() {
        return this.containingMap + "#" + this.title;
    }
    _clone() {
        let newRefEl = this.refElement.cloneNode(true);
        newRefEl.setAttribute("data-id", uuidv4());
        let newNode = new TopicReferenceNode({
            refElement: newRefEl,
            containingMap: this.containingMap,
            context: this.context,
        });
        for (let child of this.children) {
            newNode.children.push(child.copy());
        }
        return newNode;
    }
    copy() {
        this.context.buffer = this._clone();
    }
    remove() {
        this.structuralElement.remove();
    }
    _fileToRefElement(file) {
        if (file.rootElementName == "map") {
            return createRefElement("mapref", file);
        }
        if (file.rootElementName == "glossentry") {
            return createRefElement("glossref", file);
        }
        else {
            return createRefElement("topicref", file);
        }
    }
    insertNodeFromFile(file) {
        let refEl = this._fileToRefElement(file);
        this.structuralElement.appendChild(refEl);
    }
}
function uuidv4() {
    throw new Error("Function not implemented.");
}
//# sourceMappingURL=ditamap-node.js.map